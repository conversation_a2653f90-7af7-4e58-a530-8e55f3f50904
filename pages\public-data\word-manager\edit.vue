<template>
	<view class="uni-container">
		<uni-forms ref="form" :model="formData" validateTrigger="bind" :rules="rules">
			<uni-forms-item name="word" label="单词" required>
				<uni-easyinput v-model="formData.word_info.word" placeholder="请输入单词" :maxlength="50" trim="both" />
			</uni-forms-item>

			<uni-forms-item name="phonetic" label="音标">
				<uni-easyinput v-model="formData.word_info.phonetic" placeholder="请输入音标，如：/ˈæpl/" :maxlength="100" trim="both" />
			</uni-forms-item>

			<uni-forms-item name="translation" label="翻译" required>
				<uni-easyinput v-model="formData.word_info.translation" placeholder="请输入中文翻译" :maxlength="200" trim="both" />
			</uni-forms-item>

			<uni-forms-item name="example" label="例句">
				<uni-easyinput v-model="formData.word_info.example" type="textarea" placeholder="请输入英文例句" :maxlength="500" />
			</uni-forms-item>

			<uni-forms-item name="exampleTranslation" label="例句翻译">
				<uni-easyinput v-model="formData.word_info.exampleTranslation" type="textarea" placeholder="请输入例句的中文翻译" :maxlength="500" />
			</uni-forms-item>

			<uni-forms-item name="difficulty" label="难度等级" required>
				<uni-data-select v-model="formData.word_info.difficulty" :localdata="difficultyOptions" placeholder="请选择难度等级" />
			</uni-forms-item>

			<!-- 🎯 新增：单词分类 -->
			<uni-forms-item name="category" label="单词分类">
				<uni-data-select v-model="formData.category" :localdata="categoryOptions" placeholder="请选择分类" />
			</uni-forms-item>

			<!-- 🎯 新增：访问权限控制 -->
			<uni-forms-item name="accessLevel" label="访问权限" required>
				<uni-data-select v-model="formData.accessLevel" :localdata="accessLevelOptions" placeholder="请选择访问权限" />
				<view class="uni-form-item-tips">
					私有：仅自己可见；校区内：本校区用户可见；公开：所有用户可见
				</view>
			</uni-forms-item>

			<uni-forms-item name="tags" label="标签" class="flex-center-x">
				<uni-data-checkbox v-model="formData.tags" :localdata="tagOptions" :multiple="true" :max="5" />
				<view class="uni-form-item-tips">
					最多选择5个标签
				</view>
			</uni-forms-item>

			<uni-forms-item name="audioFile" label="单词发音">
				<view class="media-upload-section">
					<!-- 上传按钮区域 -->
					<view class="upload-buttons">
						<view class="upload-btn" @click="chooseAudioFile('audioFile')">
							<uni-icons type="cloud-upload" size="18" color="#409eff"></uni-icons>
							<text class="btn-text">选择音频</text>
						</view>
					</view>

					<!-- 文件预览区域 -->
					<view v-if="formData.audioFile && formData.audioFile.length > 0" class="media-preview-area">
						<view class="preview-grid">
							<view class="preview-item">
								<view class="file-preview" @click="previewMedia(formData.audioFile[0], 'audio')">
									<view class="file-icon">
										<uni-icons type="sound" size="20" color="#ff9a00"></uni-icons>
									</view>
									<text class="file-name">{{ getShortFileName(formData.audioFile[0].name) }}</text>
									<view class="remove-btn" @click.stop="handleFileDelete('audioFile')">
										<uni-icons type="close" size="12" color="#fff"></uni-icons>
									</view>
								</view>
							</view>
						</view>
					</view>

					<!-- 支持格式提示 -->
					<view class="format-tips">
						<text class="tips-text">支持格式：mp3, wav, m4a, aac，建议大小不超过5MB</text>
					</view>
				</view>
			</uni-forms-item>

			<uni-forms-item name="exampleAudioFile" label="例句发音">
				<view class="media-upload-section">
					<!-- 上传按钮区域 -->
					<view class="upload-buttons">
						<view class="upload-btn" @click="chooseAudioFile('exampleAudioFile')">
							<uni-icons type="cloud-upload" size="18" color="#409eff"></uni-icons>
							<text class="btn-text">选择音频</text>
						</view>
					</view>

					<!-- 文件预览区域 -->
					<view v-if="formData.exampleAudioFile && formData.exampleAudioFile.length > 0" class="media-preview-area">
						<view class="preview-grid">
							<view class="preview-item">
								<view class="file-preview" @click="previewMedia(formData.exampleAudioFile[0], 'audio')">
									<view class="file-icon">
										<uni-icons type="sound" size="20" color="#ff9a00"></uni-icons>
									</view>
									<text class="file-name">{{ getShortFileName(formData.exampleAudioFile[0].name) }}</text>
									<view class="remove-btn" @click.stop="handleFileDelete('exampleAudioFile')">
										<uni-icons type="close" size="12" color="#fff"></uni-icons>
									</view>
								</view>
							</view>
						</view>
					</view>

					<!-- 支持格式提示 -->
					<view class="format-tips">
						<text class="tips-text">支持格式：mp3, wav, m4a, aac，建议大小不超过5MB</text>
					</view>
				</view>
			</uni-forms-item>

			<uni-forms-item name="imageFile" label="单词图片">
				<view class="media-upload-section">
					<!-- 上传按钮区域 -->
					<view class="upload-buttons">
						<view class="upload-btn" @click="chooseImageFile">
							<uni-icons type="cloud-upload" size="18" color="#409eff"></uni-icons>
							<text class="btn-text">选择图片</text>
						</view>
					</view>

					<!-- 文件预览区域 -->
					<view v-if="formData.imageFile && formData.imageFile.length > 0" class="media-preview-area">
						<view class="preview-grid">
							<view class="preview-item">
								<!-- 图片预览 -->
								<view class="image-preview" @click="previewMedia(formData.imageFile[0], 'image')">
									<image :src="formData.imageFile[0].path" class="preview-image" mode="aspectFill"></image>
									<view class="remove-btn" @click.stop="handleFileDelete('imageFile')">
										<uni-icons type="close" size="12" color="#fff"></uni-icons>
									</view>
								</view>
							</view>
						</view>
					</view>

					<!-- 支持格式提示 -->
					<view class="format-tips">
						<text class="tips-text">支持格式：jpg, jpeg, png, gif, webp，建议大小不超过2MB</text>
					</view>
				</view>
			</uni-forms-item>

			<uni-forms-item name="isPublic" label="是否公开">
				<switch :checked="formData.isPublic" @change="e => formData.isPublic = e.detail.value" />
			</uni-forms-item>

			<!-- 操作按钮 -->
			<view class="uni-button-group">
				<button style="width: 100px;" type="primary" class="uni-button" @click="submitForm">提交</button>
				<navigator open-type="navigateBack" style="margin-left: 15px;">
					<button style="width: 100px;" class="uni-button">返回</button>
				</navigator>
			</view>
		</uni-forms>
	</view>

	<!-- 预览弹窗 -->
	<uni-popup ref="previewPopup" type="dialog">
		<uni-popup-dialog title="文件预览" mode="base" :before-close="true" @close="handlePreviewClose" :showCancelButton="true" :showConfirmButton="false">
			<view class="preview-container">
				<!-- 图片预览 -->
				<image v-if="currentPreview.type === 'image'" :src="currentPreview.url" mode="aspectFit" class="preview-image" />

				<!-- 音频预览 -->
				<view v-else-if="currentPreview.type === 'audio'" class="preview-audio">
					<view class="audio-player">
						<view class="audio-info">
							<text class="audio-title">{{ currentPreview.name || '音频文件' }}</text>
						</view>
						<view class="audio-controls">
							<button class="uni-button" type="primary" size="mini" @click="handleAudioPlay">
								{{ isPlaying ? '暂停' : '播放' }}
							</button>
						</view>
					</view>
				</view>
			</view>
		</uni-popup-dialog>
	</uni-popup>

	<!-- #ifndef H5 -->
	<fix-window />
	<!-- #endif -->
</template>

<script>
import i18n from '@/i18n'
// 🎯 引入工具函数
import { getShortFileName } from '@/utils/file-utils.js'
import {
	handleFileUpload,
	chooseAudioFile,
	chooseImageFile
} from '@/utils/upload-utils.js'
import {
	checkWordExists,
	createWordData,
	saveWordToDatabase
} from '@/utils/word-db-utils.js'

const db = uniCloud.database();
const dbCollectionName = 'word-files';

export default {
	i18n,
		data() {
			return {
				// 当前文件夹ID，用于确定单词添加位置
				currentFolder: 'root',

				// 表单数据
				formData: {
					_id: '',
					name: '',
					type: 'word',
					parent_id: '',
					category: '其他', // 🎯 新增：单词分类
					accessLevel: 'private', // 🎯 新增：访问权限，默认私有
					word_info: {
						word: '',
						phonetic: '',
						translation: '',
						example: '',
						exampleTranslation: '',
						difficulty: 1,
						audioUrl: '',
						exampleAudioUrl: '',
						imageUrl: ''
					},
					isPublic: false,
					tags: [],
					audioFile: [],
					exampleAudioFile: [],
					imageFile: []
				},

				// 音视频预览相关
				currentPreview: {
					type: '',
					url: '',
					name: ''
				},
				isPlaying: false,
				audioContext: null,

				// 表单验证规则
				rules: {
					'word_info.word': {
						rules: [{
							required: true,
							errorMessage: '请输入单词'
						}, {
							pattern: /^[a-zA-Z\s\-']+$/,
							errorMessage: '单词只能包含英文字母、空格、连字符和撇号'
						}]
					},
					'word_info.translation': {
						rules: [{
							required: true,
							errorMessage: '请输入翻译'
						}]
					},
					'word_info.difficulty': {
						rules: [{
							required: true,
							errorMessage: '请选择难度等级'
						}]
					},
					accessLevel: {
						rules: [{
							required: true,
							errorMessage: '请选择访问权限'
						}]
					}
				},

				// 🎯 难度等级选项
				difficultyOptions: [
					{ value: 1, text: '简单' },
					{ value: 2, text: '中等' },
					{ value: 3, text: '困难' },
					{ value: 4, text: '高级' },
					{ value: 5, text: '专家' }
				],

				// 🎯 新增：单词分类选项
				categoryOptions: [
					{ value: '基础词汇', text: '基础词汇' },
					{ value: '高级词汇', text: '高级词汇' },
					{ value: '专业词汇', text: '专业词汇' },
					{ value: '其他', text: '其他' }
				],

				// 🎯 新增：访问权限选项
				accessLevelOptions: [
					{ value: 'private', text: '私有（仅自己可见）' },
					{ value: 'campus', text: '校区内可见' },
					{ value: 'public', text: '公开（所有人可见）' }
				],

				// 标签选项
				tagOptions: [
					{ value: 'english', text: '英语' },
					{ value: 'math', text: '数学' },
					{ value: 'science', text: '科学' },
					{ value: 'history', text: '历史' },
					{ value: 'geography', text: '地理' },
					{ value: 'food', text: '食物' },
					{ value: 'animal', text: '动物' },
					{ value: 'fruit', text: '水果' },
					{ value: 'occupation', text: '职业' },
					{ value: 'color', text: '颜色' },
					{ value: 'number', text: '数字' },
					{ value: 'family', text: '家庭' },
					{ value: 'body', text: '身体部位' },
					{ value: 'weather', text: '天气' },
					{ value: 'time', text: '时间' },
					{ value: 'transport', text: '交通工具' }
				]
			}
		},
		onLoad(options) {
			// 初始化用户信息
			this.initUserInfo()

			// 如果有ID，则加载数据
			if (options.id) {
				this.getDetail(options.id);
			}
			
			// 如果有父ID，则设置父ID
			if (options.parent_id) {
				this.formData.parent_id = options.parent_id;
				this.currentFolder = options.parent_id;
			}
			
			// 加载标签选项
			this.loadTagOptions();
		},
		methods: {
			/**
			 * 初始化用户信息
			 */
			initUserInfo() {
				const userInfo = uni.getStorageSync('uni-id-pages-userInfo')
				if (!userInfo || !userInfo._id) {
					uni.showModal({
						title: '提示',
						content: '请先登录后再添加单词',
						showCancel: false,
						success: () => {
							uni.navigateBack()
						}
					})
					return
				}
			},

			/**
			 * 加载标签选项
			 */
			async loadTagOptions() {
				try {
					const { result } = await db.collection('resource-tags')
						.where({
							type: db.command.in(['word', 'general'])
						})
						.orderBy('sort', 'asc')
						.get();
					
					if (result && result.data) {
						this.tagOptions = result.data.map(item => ({
							value: item.tag_value,
							text: item.name
						}));
					}
				} catch (e) {
					console.error('加载标签选项失败:', e);
				}
			},

			/**
			 * 🎯 选择音频文件 - 使用 utils 中的函数
			 * @param {String} fieldName 字段名称 (audioFile 或 exampleAudioFile)
			 */
			async chooseAudioFile(fieldName) {
				try {
					await chooseAudioFile(async (files) => {
						await this.handleFileUpload(files, fieldName);
					});
				} catch (error) {
					if (error.message !== '选择文件失败') {
						console.error('选择音频文件失败:', error);
						uni.showToast({
							title: '选择文件失败',
							icon: 'none'
						});
					}
				}
			},

			/**
			 * 🎯 选择图片文件 - 使用 utils 中的函数
			 */
			async chooseImageFile() {
				try {
					await chooseImageFile(async (files) => {
						await this.handleFileUpload(files, 'imageFile');
					});
				} catch (error) {
					if (error.message !== '选择图片失败') {
						console.error('选择图片文件失败:', error);
						uni.showToast({
							title: '选择文件失败',
							icon: 'none'
						});
					}
				}
			},

			/**
			 * 🎯 文件上传处理 - 编辑页面专用逻辑
			 * @param {Array} files 文件数组
			 * @param {String} fieldName 字段名称
			 */
			async handleFileUpload(files, fieldName) {
				uni.showLoading({
					title: '上传文件中...',
					mask: true
				});

				try {
					// 检查是否有工具函数可用
					if (typeof handleFileUpload === 'function') {
						// 使用 utils 中的统一上传函数
						const fileInfo = await handleFileUpload(files, fieldName, 'word-files');
						// 更新表单数据
						this.formData[fieldName] = [fileInfo];
					} else {
						// 备用方案：直接处理文件
						const file = files[0];
						console.log('处理文件:', file);

						// 如果是编辑现有文件，可能已经有URL
						if (file.url || file.path) {
							this.formData[fieldName] = [{
								name: file.name || `${fieldName}_file`,
								path: file.path || file.url,
								url: file.url || file.path,
								size: file.size || 0,
								type: file.type || 'application/octet-stream'
							}];
						} else {
							// 新上传的文件需要上传到云存储
							const uploadResult = await this.uploadFileToCloud(file, fieldName);
							this.formData[fieldName] = [uploadResult];
						}
					}

					console.log(`${fieldName} 文件处理完成:`, this.formData[fieldName]);

					uni.showToast({
						title: '文件处理成功',
						icon: 'success'
					});

				} catch (err) {
					console.error('文件处理失败:', err);
					uni.showToast({
						title: err.message || '文件处理失败',
						icon: 'none'
					});
				} finally {
					uni.hideLoading();
				}
			},

			/**
			 * 上传文件到云存储
			 */
			async uploadFileToCloud(file, fieldName) {
				try {
					// 生成云存储路径
					const timestamp = Date.now();
					const fileName = file.name || `${fieldName}_${timestamp}`;
					const cloudPath = `word-files/${fieldName}s/${timestamp}_${fileName}`;

					// 上传到云存储
					const result = await uniCloud.uploadFile({
						filePath: file.path || file.url,
						cloudPath: cloudPath
					});

					if (!result.fileID) {
						throw new Error('文件上传失败');
					}

					// 获取文件访问链接
					const { fileList } = await uniCloud.getTempFileURL({
						fileList: [result.fileID]
					});

					if (!fileList || !fileList[0] || !fileList[0].tempFileURL) {
						throw new Error('获取文件链接失败');
					}

					return {
						name: fileName,
						path: fileList[0].tempFileURL,
						url: fileList[0].tempFileURL,
						fileID: result.fileID,
						size: file.size || 0,
						type: file.type || 'application/octet-stream'
					};
				} catch (e) {
					console.error('上传文件到云存储失败:', e);
					throw new Error('上传文件失败: ' + e.message);
				}
			},

			/**
			 * 处理文件删除
			 * @param {String} fieldName 字段名称
			 */
			handleFileDelete(fieldName) {
				// 清空对应字段的文件数组
				this.formData[fieldName] = [];
			},

			/**
			 * 预览媒体文件
			 * @param {Object} media 媒体文件对象
			 * @param {String} type 媒体类型 (audio/image)
			 */
			previewMedia(media, type) {
				if (!media) return;

				const fileUrl = media.path; // 使用path作为预览地址
				const fileName = media.name || '未命名文件';

				if (type === 'image') {
					uni.previewImage({
						urls: [fileUrl],
						current: 0
					});
				} else if (type === 'audio') {
					this.currentPreview = {
						type: 'audio',
						url: fileUrl,
						name: fileName
					};

					if (this.audioContext) {
						this.audioContext.destroy();
					}
					this.audioContext = uni.createInnerAudioContext();
					this.audioContext.src = fileUrl;
					this.isPlaying = false;

					// 打开预览弹窗
					this.$refs.previewPopup.open();
				} else {
					uni.showToast({
						title: '暂不支持此类型文件预览',
						icon: 'none'
					});
				}
			},

			/**
			 * 处理音频播放
			 */
			handleAudioPlay() {
				if (!this.audioContext) {
					this.audioContext = uni.createInnerAudioContext();
					this.audioContext.src = this.currentPreview.url;
					this.audioContext.onEnded(() => {
						this.isPlaying = false;
					});
					this.audioContext.onError(() => {
						this.isPlaying = false;
						uni.showToast({
							title: '音频播放失败',
							icon: 'none'
						});
					});
				}

				if (this.isPlaying) {
					this.audioContext.pause();
				} else {
					this.audioContext.play();
				}
				this.isPlaying = !this.isPlaying;
			},

			/**
			 * 处理预览关闭
			 */
			handlePreviewClose() {
				// 关闭音频播放
				if (this.audioContext) {
					this.audioContext.destroy();
					this.audioContext = null;
				}
				this.isPlaying = false;

				// 重置预览状态
				this.currentPreview = {
					type: '',
					url: '',
					name: ''
				};
			},

			/**
			 * 🎯 获取短文件名 - 使用 utils 中的函数
			 */
			getShortFileName,

			/**
			 * 备用方案：本地检查单词是否存在
			 */
			async checkWordExistsLocal(word) {
				try {
					const result = await db.collection(dbCollectionName)
						.where({
							type: 'word',
							'word_info.word': word,
							parent_id: this.currentFolder || 'root'
						})
						.limit(1)
						.get()

					if (result.result.data && result.result.data.length > 0) {
						throw new Error(`单词 "${word}" 在当前位置已存在`)
					}
				} catch (error) {
					if (error.message.includes('已存在')) {
						throw error
					}
					// 其他错误忽略，继续执行
					console.warn('检查单词重复时出错:', error)
				}
			},

			/**
			 * 备用方案：本地创建单词数据
			 */
			async createWordDataLocal(userInfo, audioUrl, exampleAudioUrl, imageUrl) {
				// 获取选中标签的完整信息
				const selectedTags = this.formData.tags.map(tagValue => {
					const tagInfo = this.tagOptions.find(tag => tag.value === tagValue)
					return tagInfo ? tagInfo.text : tagValue
				})

				// 构建完整路径
				const fullPath = await this.buildFullPathLocal(this.currentFolder, this.formData.word_info.word)

				// 准备单词数据
				return {
					name: this.formData.word_info.word, // 资源名称使用单词本身
					type: 'word', // 资源类型为单词
					source: 'manual', // 手动添加
					parent_id: this.currentFolder || 'root',
					owner_id: userInfo._id,
					owner_name: userInfo.nickname || userInfo.username || '未知用户',
					full_path: fullPath,
					ancestors: await this.getAncestorsLocal(this.currentFolder),
					tags: selectedTags,
					isPublic: this.formData.accessLevel === 'public',
					category: this.formData.category,
					accessLevel: this.formData.accessLevel,
					// 单词特有信息
					word_info: {
						word: this.formData.word_info.word,
						phonetic: this.formData.word_info.phonetic,
						translation: this.formData.word_info.translation,
						example: this.formData.word_info.example,
						exampleTranslation: this.formData.word_info.exampleTranslation,
						difficulty: this.formData.word_info.difficulty,
						audioUrl: audioUrl,
						exampleAudioUrl: exampleAudioUrl,
						imageUrl: imageUrl
					}
				}
			},

			/**
			 * 备用方案：构建完整路径
			 */
			async buildFullPathLocal(parentId, name) {
				if (!parentId || parentId === 'root') {
					return name
				}

				try {
					const result = await db.collection(dbCollectionName)
						.doc(parentId)
						.field('full_path')
						.get()

					if (result.result.data && result.result.data.length > 0) {
						const parentPath = result.result.data[0].full_path
						return `${parentPath}/${name}`
					}
				} catch (error) {
					console.warn('构建完整路径时出错:', error)
				}

				return name
			},

			/**
			 * 备用方案：获取祖先节点列表
			 */
			async getAncestorsLocal(parentId) {
				if (!parentId || parentId === 'root') {
					return []
				}

				try {
					const result = await db.collection(dbCollectionName)
						.doc(parentId)
						.field('ancestors')
						.get()

					if (result.result.data && result.result.data.length > 0) {
						const parentAncestors = result.result.data[0].ancestors || []
						return [...parentAncestors, parentId]
					}
				} catch (error) {
					console.warn('获取祖先节点时出错:', error)
				}

				return [parentId]
			},
			/**
			 * 获取详情
			 */
			getDetail(id) {
				uni.showLoading({
					mask: true
				});
				db.collection(dbCollectionName).doc(id).get().then(res => {
					uni.hideLoading();
					const data = res.result.data[0];
					if (data) {
						// 合并数据，确保新字段有默认值
						this.formData = Object.assign({}, this.formData, data);

						// 🎯 确保新增字段有默认值
						if (!this.formData.category) {
							this.formData.category = '其他';
						}
						if (!this.formData.accessLevel) {
							// 根据 isPublic 字段设置访问权限
							this.formData.accessLevel = this.formData.isPublic ? 'public' : 'private';
						}

						// 🎯 处理现有文件URL，转换为文件对象格式
						this.convertUrlsToFileObjects(data);
					}
				}).catch(err => {
					uni.hideLoading();
					uni.showModal({
						content: err.message || '请求服务失败',
						showCancel: false
					});
				});
			},

			/**
			 * 🎯 将现有的文件URL转换为文件对象格式，用于编辑时显示
			 */
			convertUrlsToFileObjects(data) {
				// 处理单词音频
				if (data.word_info && data.word_info.audioUrl) {
					this.formData.audioFile = [{
						name: '单词发音.mp3',
						path: data.word_info.audioUrl,
						url: data.word_info.audioUrl,
						size: 0,
						type: 'audio/mp3'
					}];
				}

				// 处理例句音频
				if (data.word_info && data.word_info.exampleAudioUrl) {
					this.formData.exampleAudioFile = [{
						name: '例句发音.mp3',
						path: data.word_info.exampleAudioUrl,
						url: data.word_info.exampleAudioUrl,
						size: 0,
						type: 'audio/mp3'
					}];
				}

				// 处理图片
				if (data.word_info && data.word_info.imageUrl) {
					this.formData.imageFile = [{
						name: '单词图片.jpg',
						path: data.word_info.imageUrl,
						url: data.word_info.imageUrl,
						size: 0,
						type: 'image/jpeg'
					}];
				}
			},
			/**
			 * 触发表单提交
			 */
			submitForm() {
				console.log('提交表单，检查文件状态:', {
					audioFile: this.formData.audioFile,
					exampleAudioFile: this.formData.exampleAudioFile,
					imageFile: this.formData.imageFile
				})

				// 🎯 使用 validate 方法代替已废弃的 submit 方法
				this.$refs.form.validate().then(res => {
					console.log('表单验证成功:', res);
					this.submit({ detail: { errors: null } });
				}).catch(errors => {
					console.log('表单验证失败:', errors);
					// 验证失败时不执行提交
				});
			},

			/**
			 * 🎯 表单提交 - 编辑页面专用逻辑
			 * @param {Object} event 回调参数 Function(callback:{value,errors})
			 */
			async submit(event) {
				const { errors } = event.detail

				// 表单校验失败页面会提示报错 ，要停止表单提交逻辑
				if (errors) {
					return
				}
				try {
					uni.showLoading({
						title: '提交中...',
						mask: true
					})

					// 获取用户信息
					const userInfo = uni.getStorageSync('uni-id-pages-userInfo')
					if (!userInfo || !userInfo._id) {
						throw new Error('用户信息无效，请重新登录')
					}

					console.log('提交前文件状态检查:', {
						audioFile: this.formData.audioFile,
						exampleAudioFile: this.formData.exampleAudioFile,
						imageFile: this.formData.imageFile
					});

					// 🎯 检查单词是否已存在
					if (!this.formData._id) {
						// 检查是否有工具函数可用
						if (typeof checkWordExists === 'function') {
							const wordExists = await checkWordExists(this.formData.word_info.word, this.currentFolder)
							if (wordExists) {
								throw new Error(`单词 "${this.formData.word_info.word}" 在当前位置已存在`)
							}
						} else {
							// 备用检查逻辑
							await this.checkWordExistsLocal(this.formData.word_info.word)
						}
					}

					// 🎯 处理文件URL
					let audioUrl = '';
					let exampleAudioUrl = '';
					let imageUrl = '';

					// 处理音频文件
					if (this.formData.audioFile && this.formData.audioFile.length > 0) {
						const audioFile = this.formData.audioFile[0];
						audioUrl = audioFile.url || audioFile.path || '';
					}

					// 处理例句音频文件
					if (this.formData.exampleAudioFile && this.formData.exampleAudioFile.length > 0) {
						const exampleAudioFile = this.formData.exampleAudioFile[0];
						exampleAudioUrl = exampleAudioFile.url || exampleAudioFile.path || '';
					}

					// 处理图片文件
					if (this.formData.imageFile && this.formData.imageFile.length > 0) {
						const imageFile = this.formData.imageFile[0];
						imageUrl = imageFile.url || imageFile.path || '';
					}

					console.log('处理后的文件URL:', { audioUrl, exampleAudioUrl, imageUrl });

					// 🎯 构建单词数据
					let wordData;
					if (typeof createWordData === 'function') {
						// 使用工具函数
						const formDataForUtils = {
							word: this.formData.word_info.word,
							phonetic: this.formData.word_info.phonetic,
							translation: this.formData.word_info.translation,
							example: this.formData.word_info.example,
							exampleTranslation: this.formData.word_info.exampleTranslation,
							difficulty: this.formData.word_info.difficulty,
							category: this.formData.category,
							accessLevel: this.formData.accessLevel,
							tags: this.formData.tags,
							audioFile: this.formData.audioFile,
							exampleAudioFile: this.formData.exampleAudioFile,
							imageFile: this.formData.imageFile
						}
						wordData = await createWordData(formDataForUtils, userInfo, this.currentFolder)
					} else {
						// 备用方案：手动构建数据
						wordData = await this.createWordDataLocal(userInfo, audioUrl, exampleAudioUrl, imageUrl)
					}

					console.log('准备保存单词数据到 word-files 表:', wordData)

					// 🎯 保存到数据库
					let dbResult;
					if (this.formData._id) {
						// 更新数据
						wordData.update_date = Date.now()
						dbResult = await db.collection(dbCollectionName).doc(this.formData._id).update(wordData)
					} else {
						// 添加数据
						if (typeof saveWordToDatabase === 'function') {
							dbResult = await saveWordToDatabase(wordData)
						} else {
							// 备用保存逻辑
							wordData.create_date = Date.now()
							wordData.update_date = Date.now()
							wordData.status = 'normal'
							dbResult = await db.collection(dbCollectionName).add(wordData)
						}
					}
					console.log('数据库保存结果:', dbResult)

					uni.showToast({
						title: this.formData._id ? '更新成功' : '添加成功',
						icon: 'success',
						duration: 2000
					})

					// 延迟返回上一页
					setTimeout(() => uni.navigateBack(), 500)

				} catch (err) {
					console.error('提交失败:', err);
					uni.showModal({
						content: err.message || '请求服务失败',
						showCancel: false
					})
				} finally {
					uni.hideLoading()
				}
			},



		},
		// 页面卸载时清理资源
		onUnload() {
			if (this.wordAudioContext) {
				this.wordAudioContext.destroy();
			}
			if (this.exampleAudioContext) {
				this.exampleAudioContext.destroy();
			}
		}
	}
</script>

<style lang="scss">
.uni-container {
	padding: 15px;
}

.flex-center-x {
	display: flex;
	flex-direction: row;
	align-items: center;
}

.uni-form-item-tips {
	font-size: 12px;
	color: #909399;
	margin-top: 5px;
	line-height: 1.4;
}

.uni-button-group {
	margin-top: 15px;
	display: flex;
}

.link-btn {
	color: #007AFF;
	cursor: pointer;
}

/* 媒体上传区域样式 */
.media-upload-section {
	border: 1px solid #eee;
	border-radius: 8px;
	overflow: hidden;
}

.upload-buttons {
	display: flex;
	gap: 10px;
	padding: 15px;
}

.upload-btn {
	display: flex;
	align-items: center;
	gap: 6px;
	padding: 8px 16px;
	border: 1px solid #ddd;
	border-radius: 6px;
	cursor: pointer;
	transition: all 0.3s;
	background-color: #fff;
}

.upload-btn:hover {
	border-color: #409eff;
	background-color: #f0f8ff;
}

.btn-text {
	font-size: 14px;
	color: #333;
}

.media-preview-area {
	padding: 15px;
	border-top: 1px solid #eee;
}

.preview-grid {
	display: flex;
	flex-wrap: wrap;
	gap: 10px;
	margin-bottom: 15px;
}

.preview-item {
	position: relative;
}

.image-preview,
.file-preview {
	position: relative;
	width: 80px;
	height: 80px;
	border: 1px solid #eee;
	border-radius: 6px;
	overflow: hidden;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	box-sizing: border-box;
	cursor: pointer;
	transition: all 0.3s;
}

.image-preview:hover,
.file-preview:hover {
	transform: scale(1.05);
	box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.image-preview {
	padding: 0;
}

.file-preview {
	background-color: #fafafa;
	padding: 8px;
}

.preview-image {
	width: 100%;
	height: 100%;
	object-fit: cover;
	border-radius: 5px;
}

.file-icon {
	margin-bottom: 4px;
}

.file-name {
	font-size: 10px;
	color: #666;
	text-align: center;
	line-height: 1.2;
	word-break: break-all;
}

.remove-btn {
	position: absolute;
	top: -6px;
	right: -6px;
	width: 20px;
	height: 20px;
	border-radius: 50%;
	background-color: #f56c6c;
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: pointer;
	box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.format-tips {
	padding: 12px 20px;
	background-color: #f8f9fa;
	border-top: 1px solid #eee;
}

.tips-text {
	font-size: 12px;
	color: #909399;
	line-height: 1.4;
}

/* 预览弹窗样式 */
.preview-container {
	padding: 15px;
	max-height: 80vh;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
}

.preview-image {
	max-width: 100%;
	max-height: 70vh;
	object-fit: contain;
}

.preview-audio {
	width: 100%;
	padding: 15px;
}

.audio-player {
	background: #f8f8f8;
	border-radius: 8px;
	padding: 15px;
	display: flex;
	flex-direction: column;
	gap: 10px;
}

.audio-info {
	display: flex;
	align-items: center;
	gap: 10px;
}

.audio-title {
	font-size: 14px;
	color: #333;
}

.audio-controls {
	display: flex;
	gap: 10px;
	justify-content: center;
}
</style>