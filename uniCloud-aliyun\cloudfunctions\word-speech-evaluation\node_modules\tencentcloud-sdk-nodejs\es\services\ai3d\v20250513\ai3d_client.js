import { AbstractClient } from "../../../common/abstract_client";
export class Client extends AbstractClient {
    constructor(clientConfig) {
        super("ai3d.tencentcloudapi.com", "2025-05-13", clientConfig);
    }
    async SubmitHunyuanTo3DJob(req, cb) {
        return this.request("SubmitHunyuanTo3DJob", req, cb);
    }
    async QueryHunyuanTo3DJob(req, cb) {
        return this.request("QueryHunyuanTo3DJob", req, cb);
    }
}
