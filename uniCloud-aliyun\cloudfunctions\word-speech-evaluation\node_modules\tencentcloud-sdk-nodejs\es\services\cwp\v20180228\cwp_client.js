import { AbstractClient } from "../../../common/abstract_client";
export class Client extends AbstractClient {
    constructor(clientConfig) {
        super("cwp.tencentcloudapi.com", "2018-02-28", clientConfig);
    }
    async DescribeAssetPortInfoList(req, cb) {
        return this.request("DescribeAssetPortInfoList", req, cb);
    }
    async DescribeAESKey(req, cb) {
        return this.request("DescribeAESKey", req, cb);
    }
    async ModifyRansomDefenseStrategyStatus(req, cb) {
        return this.request("ModifyRansomDefenseStrategyStatus", req, cb);
    }
    async DescribeRansomDefenseStrategyList(req, cb) {
        return this.request("DescribeRansomDefenseStrategyList", req, cb);
    }
    async ExportAssetJarList(req, cb) {
        return this.request("ExportAssetJarList", req, cb);
    }
    async DescribeBanMode(req, cb) {
        return this.request("DescribeBanMode", req, cb);
    }
    async StopAssetScan(req, cb) {
        return this.request("StopAssetScan", req, cb);
    }
    async KeysLocalStorage(req, cb) {
        return this.request("KeysLocalStorage", req, cb);
    }
    async ExportJavaMemShellPlugins(req, cb) {
        return this.request("ExportJavaMemShellPlugins", req, cb);
    }
    async DeleteSearchTemplate(req, cb) {
        return this.request("DeleteSearchTemplate", req, cb);
    }
    async CheckBashRuleParams(req, cb) {
        return this.request("CheckBashRuleParams", req, cb);
    }
    async DescribeScanTaskDetails(req, cb) {
        return this.request("DescribeScanTaskDetails", req, cb);
    }
    async DescribeServerRelatedDirInfo(req, cb) {
        return this.request("DescribeServerRelatedDirInfo", req, cb);
    }
    async ExportVulDefenceList(req, cb) {
        return this.request("ExportVulDefenceList", req, cb);
    }
    async DescribeScanSchedule(req, cb) {
        return this.request("DescribeScanSchedule", req, cb);
    }
    async CreateBaselineStrategy(req, cb) {
        return this.request("CreateBaselineStrategy", req, cb);
    }
    async DescribeHostInfo(req, cb) {
        return this.request("DescribeHostInfo", req, cb);
    }
    async DescribeImportMachineInfo(req, cb) {
        return this.request("DescribeImportMachineInfo", req, cb);
    }
    async DescribeBaselineRuleList(req, cb) {
        return this.request("DescribeBaselineRuleList", req, cb);
    }
    async DescribeScreenRiskAssetsTop(req, cb) {
        return this.request("DescribeScreenRiskAssetsTop", req, cb);
    }
    async DescribeAssetAppCount(req, cb) {
        return this.request("DescribeAssetAppCount", req, cb);
    }
    async DescribeBaselineHostTop(req, cb) {
        return this.request("DescribeBaselineHostTop", req, cb);
    }
    async DescribeJavaMemShellList(req, cb) {
        return this.request("DescribeJavaMemShellList", req, cb);
    }
    async DescribeBaselineDetectList(req, cb) {
        return this.request("DescribeBaselineDetectList", req, cb);
    }
    async DescribeProtectNetList(req, cb) {
        return this.request("DescribeProtectNetList", req, cb);
    }
    async DescribeReverseShellRules(req, cb) {
        return this.request("DescribeReverseShellRules", req, cb);
    }
    async DeleteBashPolicies(req, cb) {
        return this.request("DeleteBashPolicies", req, cb);
    }
    async DescribeVulDefenceOverview(req, cb) {
        return this.request("DescribeVulDefenceOverview", req, cb);
    }
    async DescribeAssetUserCount(req, cb) {
        return this.request("DescribeAssetUserCount", req, cb);
    }
    async ExportAssetMachineDetail(req, cb) {
        return this.request("ExportAssetMachineDetail", req, cb);
    }
    async DescribeAssetWebLocationCount(req, cb) {
        return this.request("DescribeAssetWebLocationCount", req, cb);
    }
    async DescribeAssetWebLocationPathList(req, cb) {
        return this.request("DescribeAssetWebLocationPathList", req, cb);
    }
    async DescribeScreenHostInvasion(req, cb) {
        return this.request("DescribeScreenHostInvasion", req, cb);
    }
    async DescribeFileTamperEventRuleInfo(req, cb) {
        return this.request("DescribeFileTamperEventRuleInfo", req, cb);
    }
    async DescribeLogIndex(req, cb) {
        return this.request("DescribeLogIndex", req, cb);
    }
    async DestroyOrder(req, cb) {
        return this.request("DestroyOrder", req, cb);
    }
    async ModifyWebHookReceiver(req, cb) {
        return this.request("ModifyWebHookReceiver", req, cb);
    }
    async DescribeScreenGeneralStat(req, cb) {
        return this.request("DescribeScreenGeneralStat", req, cb);
    }
    async DescribeBashEventsInfoNew(req, cb) {
        return this.request("DescribeBashEventsInfoNew", req, cb);
    }
    async ModifyBaselineRuleIgnore(req, cb) {
        return this.request("ModifyBaselineRuleIgnore", req, cb);
    }
    async DescribeAssetWebServiceProcessList(req, cb) {
        return this.request("DescribeAssetWebServiceProcessList", req, cb);
    }
    async ExportMaliciousRequests(req, cb) {
        return this.request("ExportMaliciousRequests", req, cb);
    }
    async DescribeAssetLoadInfo(req, cb) {
        return this.request("DescribeAssetLoadInfo", req, cb);
    }
    async FixBaselineDetect(req, cb) {
        return this.request("FixBaselineDetect", req, cb);
    }
    async DescribeBaselineRuleDetectList(req, cb) {
        return this.request("DescribeBaselineRuleDetectList", req, cb);
    }
    async ModifyRaspMaxCpu(req, cb) {
        return this.request("ModifyRaspMaxCpu", req, cb);
    }
    async DeleteLicenseRecordAll(req, cb) {
        return this.request("DeleteLicenseRecordAll", req, cb);
    }
    async DescribeOverviewStatistics(req, cb) {
        return this.request("DescribeOverviewStatistics", req, cb);
    }
    async ExportBaselineItemDetectList(req, cb) {
        return this.request("ExportBaselineItemDetectList", req, cb);
    }
    async ModifyLoginWhiteRecord(req, cb) {
        return this.request("ModifyLoginWhiteRecord", req, cb);
    }
    async DescribeBaselineScanSchedule(req, cb) {
        return this.request("DescribeBaselineScanSchedule", req, cb);
    }
    async DescribeNetAttackSetting(req, cb) {
        return this.request("DescribeNetAttackSetting", req, cb);
    }
    async ExportAssetWebFrameList(req, cb) {
        return this.request("ExportAssetWebFrameList", req, cb);
    }
    async CheckFileTamperRule(req, cb) {
        return this.request("CheckFileTamperRule", req, cb);
    }
    async DescribeNetAttackWhiteList(req, cb) {
        return this.request("DescribeNetAttackWhiteList", req, cb);
    }
    async DeletePrivilegeRules(req, cb) {
        return this.request("DeletePrivilegeRules", req, cb);
    }
    async ModifyBashPolicyStatus(req, cb) {
        return this.request("ModifyBashPolicyStatus", req, cb);
    }
    async DeleteRiskDnsEvent(req, cb) {
        return this.request("DeleteRiskDnsEvent", req, cb);
    }
    async SetLocalStorageItem(req, cb) {
        return this.request("SetLocalStorageItem", req, cb);
    }
    async DeleteLoginWhiteList(req, cb) {
        return this.request("DeleteLoginWhiteList", req, cb);
    }
    async DeleteLogExport(req, cb) {
        return this.request("DeleteLogExport", req, cb);
    }
    async DescribeExportMachines(req, cb) {
        return this.request("DescribeExportMachines", req, cb);
    }
    async ClearLocalStorage(req, cb) {
        return this.request("ClearLocalStorage", req, cb);
    }
    async DescribeAssetWebFrameCount(req, cb) {
        return this.request("DescribeAssetWebFrameCount", req, cb);
    }
    async DeleteBaselineStrategy(req, cb) {
        return this.request("DeleteBaselineStrategy", req, cb);
    }
    async DescribeLicense(req, cb) {
        return this.request("DescribeLicense", req, cb);
    }
    async ExportBashPolicies(req, cb) {
        return this.request("ExportBashPolicies", req, cb);
    }
    async DescribeMachineLicenseDetail(req, cb) {
        return this.request("DescribeMachineLicenseDetail", req, cb);
    }
    async ModifyReverseShellRulesAggregation(req, cb) {
        return this.request("ModifyReverseShellRulesAggregation", req, cb);
    }
    async SwitchBashRules(req, cb) {
        return this.request("SwitchBashRules", req, cb);
    }
    async DescribeAssetUserList(req, cb) {
        return this.request("DescribeAssetUserList", req, cb);
    }
    async DescribeAssetWebLocationInfo(req, cb) {
        return this.request("DescribeAssetWebLocationInfo", req, cb);
    }
    async DescribeJavaMemShellPluginInfo(req, cb) {
        return this.request("DescribeJavaMemShellPluginInfo", req, cb);
    }
    async DescribeBruteAttackList(req, cb) {
        return this.request("DescribeBruteAttackList", req, cb);
    }
    async DescribeAssetDatabaseList(req, cb) {
        return this.request("DescribeAssetDatabaseList", req, cb);
    }
    async DescribeVulEffectHostList(req, cb) {
        return this.request("DescribeVulEffectHostList", req, cb);
    }
    async CheckFirstScanBaseline(req, cb) {
        return this.request("CheckFirstScanBaseline", req, cb);
    }
    async DescribeSearchTemplates(req, cb) {
        return this.request("DescribeSearchTemplates", req, cb);
    }
    async ExportAssetWebServiceInfoList(req, cb) {
        return this.request("ExportAssetWebServiceInfoList", req, cb);
    }
    async SeparateMalwares(req, cb) {
        return this.request("SeparateMalwares", req, cb);
    }
    async DeleteNetAttackWhiteList(req, cb) {
        return this.request("DeleteNetAttackWhiteList", req, cb);
    }
    async DescribeLogExports(req, cb) {
        return this.request("DescribeLogExports", req, cb);
    }
    async DescribeScreenMachines(req, cb) {
        return this.request("DescribeScreenMachines", req, cb);
    }
    async DescribeScreenProtectionStat(req, cb) {
        return this.request("DescribeScreenProtectionStat", req, cb);
    }
    async DescribeBashEventsNew(req, cb) {
        return this.request("DescribeBashEventsNew", req, cb);
    }
    async DescribeAssetMachineList(req, cb) {
        return this.request("DescribeAssetMachineList", req, cb);
    }
    async RemoveMachine(req, cb) {
        return this.request("RemoveMachine", req, cb);
    }
    async DescribeAssetHostTotalCount(req, cb) {
        return this.request("DescribeAssetHostTotalCount", req, cb);
    }
    async DescribeAssetWebLocationList(req, cb) {
        return this.request("DescribeAssetWebLocationList", req, cb);
    }
    async DescribeRiskDnsEventInfo(req, cb) {
        return this.request("DescribeRiskDnsEventInfo", req, cb);
    }
    async ExportBashEventsNew(req, cb) {
        return this.request("ExportBashEventsNew", req, cb);
    }
    async DescribeRiskBatchStatus(req, cb) {
        return this.request("DescribeRiskBatchStatus", req, cb);
    }
    async DescribeAssetProcessInfoList(req, cb) {
        return this.request("DescribeAssetProcessInfoList", req, cb);
    }
    async DescribeWebHookReceiver(req, cb) {
        return this.request("DescribeWebHookReceiver", req, cb);
    }
    async ModifyAutoOpenProVersionConfig(req, cb) {
        return this.request("ModifyAutoOpenProVersionConfig", req, cb);
    }
    async SetLocalStorageExpire(req, cb) {
        return this.request("SetLocalStorageExpire", req, cb);
    }
    async DescribeCanFixVulMachine(req, cb) {
        return this.request("DescribeCanFixVulMachine", req, cb);
    }
    async ModifyBaselinePolicyState(req, cb) {
        return this.request("ModifyBaselinePolicyState", req, cb);
    }
    async TestWebHookRule(req, cb) {
        return this.request("TestWebHookRule", req, cb);
    }
    async DeleteMalwareScanTask(req, cb) {
        return this.request("DeleteMalwareScanTask", req, cb);
    }
    async DescribeFileTamperRules(req, cb) {
        return this.request("DescribeFileTamperRules", req, cb);
    }
    async DescribeMachineDefenseCnt(req, cb) {
        return this.request("DescribeMachineDefenseCnt", req, cb);
    }
    async DescribeProVersionInfo(req, cb) {
        return this.request("DescribeProVersionInfo", req, cb);
    }
    async ModifyNetAttackSetting(req, cb) {
        return this.request("ModifyNetAttackSetting", req, cb);
    }
    async DeleteBanWhiteList(req, cb) {
        return this.request("DeleteBanWhiteList", req, cb);
    }
    async DescribeWebPageGeneralize(req, cb) {
        return this.request("DescribeWebPageGeneralize", req, cb);
    }
    async DescribePrivilegeEvents(req, cb) {
        return this.request("DescribePrivilegeEvents", req, cb);
    }
    async DescribeMachineInfo(req, cb) {
        return this.request("DescribeMachineInfo", req, cb);
    }
    async DescribeAssetTypeTop(req, cb) {
        return this.request("DescribeAssetTypeTop", req, cb);
    }
    async DescribeRaspRuleVuls(req, cb) {
        return this.request("DescribeRaspRuleVuls", req, cb);
    }
    async ExportRansomDefenseStrategyMachines(req, cb) {
        return this.request("ExportRansomDefenseStrategyMachines", req, cb);
    }
    async DescribeLogStorageStatistic(req, cb) {
        return this.request("DescribeLogStorageStatistic", req, cb);
    }
    async DescribeIgnoreHostAndItemConfig(req, cb) {
        return this.request("DescribeIgnoreHostAndItemConfig", req, cb);
    }
    async DescribeIgnoreBaselineRule(req, cb) {
        return this.request("DescribeIgnoreBaselineRule", req, cb);
    }
    async DescribeLogDeliveryKafkaOptions(req, cb) {
        return this.request("DescribeLogDeliveryKafkaOptions", req, cb);
    }
    async DescribeMachineRiskCnt(req, cb) {
        return this.request("DescribeMachineRiskCnt", req, cb);
    }
    async DescribeScreenBroadcasts(req, cb) {
        return this.request("DescribeScreenBroadcasts", req, cb);
    }
    async CreateWhiteListOrder(req, cb) {
        return this.request("CreateWhiteListOrder", req, cb);
    }
    async ModifyLicenseUnBinds(req, cb) {
        return this.request("ModifyLicenseUnBinds", req, cb);
    }
    async DescribeVersionCompareChart(req, cb) {
        return this.request("DescribeVersionCompareChart", req, cb);
    }
    async CreateScanMalwareSetting(req, cb) {
        return this.request("CreateScanMalwareSetting", req, cb);
    }
    async DescribeRansomDefenseTrend(req, cb) {
        return this.request("DescribeRansomDefenseTrend", req, cb);
    }
    async ExportAssetWebAppList(req, cb) {
        return this.request("ExportAssetWebAppList", req, cb);
    }
    async DescribeLicenseBindList(req, cb) {
        return this.request("DescribeLicenseBindList", req, cb);
    }
    async DescribeAssetCoreModuleInfo(req, cb) {
        return this.request("DescribeAssetCoreModuleInfo", req, cb);
    }
    async DescribeGeneralStat(req, cb) {
        return this.request("DescribeGeneralStat", req, cb);
    }
    async ModifyJavaMemShellPluginSwitch(req, cb) {
        return this.request("ModifyJavaMemShellPluginSwitch", req, cb);
    }
    async DescribeAssetDatabaseInfo(req, cb) {
        return this.request("DescribeAssetDatabaseInfo", req, cb);
    }
    async DescribeMalwareWhiteList(req, cb) {
        return this.request("DescribeMalwareWhiteList", req, cb);
    }
    async DescribeScanVulSetting(req, cb) {
        return this.request("DescribeScanVulSetting", req, cb);
    }
    async CreateMalwareWhiteList(req, cb) {
        return this.request("CreateMalwareWhiteList", req, cb);
    }
    async DeleteMachine(req, cb) {
        return this.request("DeleteMachine", req, cb);
    }
    async ModifyFileTamperEvents(req, cb) {
        return this.request("ModifyFileTamperEvents", req, cb);
    }
    async DescribeSecurityBroadcastInfo(req, cb) {
        return this.request("DescribeSecurityBroadcastInfo", req, cb);
    }
    async ScanTaskAgain(req, cb) {
        return this.request("ScanTaskAgain", req, cb);
    }
    async DescribeVulOverview(req, cb) {
        return this.request("DescribeVulOverview", req, cb);
    }
    async DescribeLogType(req, cb) {
        return this.request("DescribeLogType", req, cb);
    }
    async DescribeAttackEventInfo(req, cb) {
        return this.request("DescribeAttackEventInfo", req, cb);
    }
    async DescribeScreenAttackHotspot(req, cb) {
        return this.request("DescribeScreenAttackHotspot", req, cb);
    }
    async DeleteWebPageEventLog(req, cb) {
        return this.request("DeleteWebPageEventLog", req, cb);
    }
    async ExportBaselineFixList(req, cb) {
        return this.request("ExportBaselineFixList", req, cb);
    }
    async DescribeProVersionStatus(req, cb) {
        return this.request("DescribeProVersionStatus", req, cb);
    }
    async DescribeMachines(req, cb) {
        return this.request("DescribeMachines", req, cb);
    }
    async DescribeBaselineHostDetectList(req, cb) {
        return this.request("DescribeBaselineHostDetectList", req, cb);
    }
    async DescribeAssetWebServiceInfoList(req, cb) {
        return this.request("DescribeAssetWebServiceInfoList", req, cb);
    }
    async DescribeAssetProcessCount(req, cb) {
        return this.request("DescribeAssetProcessCount", req, cb);
    }
    async DeleteMachineTag(req, cb) {
        return this.request("DeleteMachineTag", req, cb);
    }
    async DescribeVulTrend(req, cb) {
        return this.request("DescribeVulTrend", req, cb);
    }
    async DescribeVertexDetail(req, cb) {
        return this.request("DescribeVertexDetail", req, cb);
    }
    async ModifyWebHookPolicyStatus(req, cb) {
        return this.request("ModifyWebHookPolicyStatus", req, cb);
    }
    async ExportBaselineItemList(req, cb) {
        return this.request("ExportBaselineItemList", req, cb);
    }
    async DescribeBaselineItemRiskTop(req, cb) {
        return this.request("DescribeBaselineItemRiskTop", req, cb);
    }
    async ExportAssetAppList(req, cb) {
        return this.request("ExportAssetAppList", req, cb);
    }
    async ExportBaselineWeakPasswordList(req, cb) {
        return this.request("ExportBaselineWeakPasswordList", req, cb);
    }
    async DescribeBaselineStrategyList(req, cb) {
        return this.request("DescribeBaselineStrategyList", req, cb);
    }
    async RemoveLocalStorageItem(req, cb) {
        return this.request("RemoveLocalStorageItem", req, cb);
    }
    async ExportAssetPortInfoList(req, cb) {
        return this.request("ExportAssetPortInfoList", req, cb);
    }
    async ExportAssetMachineList(req, cb) {
        return this.request("ExportAssetMachineList", req, cb);
    }
    async DescribeLogStorageRecord(req, cb) {
        return this.request("DescribeLogStorageRecord", req, cb);
    }
    async CheckBashPolicyParams(req, cb) {
        return this.request("CheckBashPolicyParams", req, cb);
    }
    async ModifyEventAttackStatus(req, cb) {
        return this.request("ModifyEventAttackStatus", req, cb);
    }
    async CreateIncidentBacktracking(req, cb) {
        return this.request("CreateIncidentBacktracking", req, cb);
    }
    async DeleteBashEvents(req, cb) {
        return this.request("DeleteBashEvents", req, cb);
    }
    async DescribeBaselineEffectHostList(req, cb) {
        return this.request("DescribeBaselineEffectHostList", req, cb);
    }
    async DeleteMaliciousRequests(req, cb) {
        return this.request("DeleteMaliciousRequests", req, cb);
    }
    async DescribeVersionStatistics(req, cb) {
        return this.request("DescribeVersionStatistics", req, cb);
    }
    async RecoverMalwares(req, cb) {
        return this.request("RecoverMalwares", req, cb);
    }
    async DeleteReverseShellRules(req, cb) {
        return this.request("DeleteReverseShellRules", req, cb);
    }
    async DescribeAssetWebAppList(req, cb) {
        return this.request("DescribeAssetWebAppList", req, cb);
    }
    async DescribeProtectDirRelatedServer(req, cb) {
        return this.request("DescribeProtectDirRelatedServer", req, cb);
    }
    async ExportBaselineHostDetectList(req, cb) {
        return this.request("ExportBaselineHostDetectList", req, cb);
    }
    async DeleteProtectDir(req, cb) {
        return this.request("DeleteProtectDir", req, cb);
    }
    async DeleteBruteAttacks(req, cb) {
        return this.request("DeleteBruteAttacks", req, cb);
    }
    async DeleteWebHookReceiver(req, cb) {
        return this.request("DeleteWebHookReceiver", req, cb);
    }
    async ExportBashEvents(req, cb) {
        return this.request("ExportBashEvents", req, cb);
    }
    async DescribeMachineGeneral(req, cb) {
        return this.request("DescribeMachineGeneral", req, cb);
    }
    async DescribeRiskDnsPolicyList(req, cb) {
        return this.request("DescribeRiskDnsPolicyList", req, cb);
    }
    async DescribeEmergencyVulList(req, cb) {
        return this.request("DescribeEmergencyVulList", req, cb);
    }
    async DescribeJavaMemShellInfo(req, cb) {
        return this.request("DescribeJavaMemShellInfo", req, cb);
    }
    async DescribeBaselineDownloadList(req, cb) {
        return this.request("DescribeBaselineDownloadList", req, cb);
    }
    async UntrustMalwares(req, cb) {
        return this.request("UntrustMalwares", req, cb);
    }
    async ModifyLogKafkaAccess(req, cb) {
        return this.request("ModifyLogKafkaAccess", req, cb);
    }
    async DescribePrivilegeRules(req, cb) {
        return this.request("DescribePrivilegeRules", req, cb);
    }
    async DescribeAssetJarList(req, cb) {
        return this.request("DescribeAssetJarList", req, cb);
    }
    async DescribeDefenceEventDetail(req, cb) {
        return this.request("DescribeDefenceEventDetail", req, cb);
    }
    async DescribeMalwareRiskOverview(req, cb) {
        return this.request("DescribeMalwareRiskOverview", req, cb);
    }
    async ModifyBanStatus(req, cb) {
        return this.request("ModifyBanStatus", req, cb);
    }
    async DescribeMachineClearHistory(req, cb) {
        return this.request("DescribeMachineClearHistory", req, cb);
    }
    async UpdateMachineTags(req, cb) {
        return this.request("UpdateMachineTags", req, cb);
    }
    async DescribeAgentInstallCommand(req, cb) {
        return this.request("DescribeAgentInstallCommand", req, cb);
    }
    async ScanAsset(req, cb) {
        return this.request("ScanAsset", req, cb);
    }
    async DeleteReverseShellEvents(req, cb) {
        return this.request("DeleteReverseShellEvents", req, cb);
    }
    async ExportScanTaskDetails(req, cb) {
        return this.request("ExportScanTaskDetails", req, cb);
    }
    async DescribeMachineRegionList(req, cb) {
        return this.request("DescribeMachineRegionList", req, cb);
    }
    async ModifyWebPageProtectSetting(req, cb) {
        return this.request("ModifyWebPageProtectSetting", req, cb);
    }
    async ExportAssetInitServiceList(req, cb) {
        return this.request("ExportAssetInitServiceList", req, cb);
    }
    async ModifyLoginWhiteInfo(req, cb) {
        return this.request("ModifyLoginWhiteInfo", req, cb);
    }
    async ExportAssetCoreModuleList(req, cb) {
        return this.request("ExportAssetCoreModuleList", req, cb);
    }
    async DescribeWarningList(req, cb) {
        return this.request("DescribeWarningList", req, cb);
    }
    async DescribeBaselineRuleIgnoreList(req, cb) {
        return this.request("DescribeBaselineRuleIgnoreList", req, cb);
    }
    async ModifyMachineRemark(req, cb) {
        return this.request("ModifyMachineRemark", req, cb);
    }
    async DescribeBaselineBasicInfo(req, cb) {
        return this.request("DescribeBaselineBasicInfo", req, cb);
    }
    async DescribeMachineRegions(req, cb) {
        return this.request("DescribeMachineRegions", req, cb);
    }
    async DescribeVulDefenceSetting(req, cb) {
        return this.request("DescribeVulDefenceSetting", req, cb);
    }
    async DescribeVulHostCountScanTime(req, cb) {
        return this.request("DescribeVulHostCountScanTime", req, cb);
    }
    async ExportBaselineRuleDetectList(req, cb) {
        return this.request("ExportBaselineRuleDetectList", req, cb);
    }
    async DescribeLogStorageConfig(req, cb) {
        return this.request("DescribeLogStorageConfig", req, cb);
    }
    async DeleteMachineClearHistory(req, cb) {
        return this.request("DeleteMachineClearHistory", req, cb);
    }
    async DescribeMachineSnapshot(req, cb) {
        return this.request("DescribeMachineSnapshot", req, cb);
    }
    async DescribeLicenseWhiteConfig(req, cb) {
        return this.request("DescribeLicenseWhiteConfig", req, cb);
    }
    async DescribeMaliciousRequestWhiteList(req, cb) {
        return this.request("DescribeMaliciousRequestWhiteList", req, cb);
    }
    async DescribeAttackTop(req, cb) {
        return this.request("DescribeAttackTop", req, cb);
    }
    async DescribeBaselineStrategyDetail(req, cb) {
        return this.request("DescribeBaselineStrategyDetail", req, cb);
    }
    async ModifyBaselinePolicy(req, cb) {
        return this.request("ModifyBaselinePolicy", req, cb);
    }
    async DescribeAssetJarInfo(req, cb) {
        return this.request("DescribeAssetJarInfo", req, cb);
    }
    async DeleteBaselinePolicy(req, cb) {
        return this.request("DeleteBaselinePolicy", req, cb);
    }
    async DescribeScanTaskStatus(req, cb) {
        return this.request("DescribeScanTaskStatus", req, cb);
    }
    async CreateLicenseOrder(req, cb) {
        return this.request("CreateLicenseOrder", req, cb);
    }
    async DescribeScreenMachineRegions(req, cb) {
        return this.request("DescribeScreenMachineRegions", req, cb);
    }
    async DescribeBaselineHostIgnoreList(req, cb) {
        return this.request("DescribeBaselineHostIgnoreList", req, cb);
    }
    async EditTags(req, cb) {
        return this.request("EditTags", req, cb);
    }
    async DescribeRaspRules(req, cb) {
        return this.request("DescribeRaspRules", req, cb);
    }
    async CreateLogExport(req, cb) {
        return this.request("CreateLogExport", req, cb);
    }
    async DescribeLoginWhiteCombinedList(req, cb) {
        return this.request("DescribeLoginWhiteCombinedList", req, cb);
    }
    async ExportNonlocalLoginPlaces(req, cb) {
        return this.request("ExportNonlocalLoginPlaces", req, cb);
    }
    async DescribeServersAndRiskAndFirstInfo(req, cb) {
        return this.request("DescribeServersAndRiskAndFirstInfo", req, cb);
    }
    async DescribeAssetAppProcessList(req, cb) {
        return this.request("DescribeAssetAppProcessList", req, cb);
    }
    async SyncMachines(req, cb) {
        return this.request("SyncMachines", req, cb);
    }
    async DescribeReverseShellEvents(req, cb) {
        return this.request("DescribeReverseShellEvents", req, cb);
    }
    async DescribeMalwareTimingScanSetting(req, cb) {
        return this.request("DescribeMalwareTimingScanSetting", req, cb);
    }
    async DeleteRiskDnsPolicy(req, cb) {
        return this.request("DeleteRiskDnsPolicy", req, cb);
    }
    async ExportVulInfo(req, cb) {
        return this.request("ExportVulInfo", req, cb);
    }
    async ExportAssetDatabaseList(req, cb) {
        return this.request("ExportAssetDatabaseList", req, cb);
    }
    async ExportFileTamperRules(req, cb) {
        return this.request("ExportFileTamperRules", req, cb);
    }
    async ModifyWarningSetting(req, cb) {
        return this.request("ModifyWarningSetting", req, cb);
    }
    async DescribeScreenProtectionCnt(req, cb) {
        return this.request("DescribeScreenProtectionCnt", req, cb);
    }
    async ModifyBashPolicy(req, cb) {
        return this.request("ModifyBashPolicy", req, cb);
    }
    async DescribeSafeInfo(req, cb) {
        return this.request("DescribeSafeInfo", req, cb);
    }
    async ExportAssetWebLocationList(req, cb) {
        return this.request("ExportAssetWebLocationList", req, cb);
    }
    async DescribeRansomDefenseBackupList(req, cb) {
        return this.request("DescribeRansomDefenseBackupList", req, cb);
    }
    async DescribeBaselineDetectOverview(req, cb) {
        return this.request("DescribeBaselineDetectOverview", req, cb);
    }
    async DescribeBaselineItemDetectList(req, cb) {
        return this.request("DescribeBaselineItemDetectList", req, cb);
    }
    async DescribeMachinesSimple(req, cb) {
        return this.request("DescribeMachinesSimple", req, cb);
    }
    async DescribeRansomDefenseStrategyDetail(req, cb) {
        return this.request("DescribeRansomDefenseStrategyDetail", req, cb);
    }
    async ExportVulDetectionExcel(req, cb) {
        return this.request("ExportVulDetectionExcel", req, cb);
    }
    async DescribeBanRegions(req, cb) {
        return this.request("DescribeBanRegions", req, cb);
    }
    async DeleteMaliciousRequestWhiteList(req, cb) {
        return this.request("DeleteMaliciousRequestWhiteList", req, cb);
    }
    async CreateBanWhiteList(req, cb) {
        return this.request("CreateBanWhiteList", req, cb);
    }
    async DescribeVulInfoCvss(req, cb) {
        return this.request("DescribeVulInfoCvss", req, cb);
    }
    async DescribeUsersConfig(req, cb) {
        return this.request("DescribeUsersConfig", req, cb);
    }
    async ModifyLogKafkaState(req, cb) {
        return this.request("ModifyLogKafkaState", req, cb);
    }
    async ExportJavaMemShells(req, cb) {
        return this.request("ExportJavaMemShells", req, cb);
    }
    async DescribeBaselineWeakPasswordList(req, cb) {
        return this.request("DescribeBaselineWeakPasswordList", req, cb);
    }
    async DescribeRecommendedProtectCpu(req, cb) {
        return this.request("DescribeRecommendedProtectCpu", req, cb);
    }
    async DescribeBaselineItemInfo(req, cb) {
        return this.request("DescribeBaselineItemInfo", req, cb);
    }
    async DescribeAssetInitServiceList(req, cb) {
        return this.request("DescribeAssetInitServiceList", req, cb);
    }
    async ExportAttackEvents(req, cb) {
        return this.request("ExportAttackEvents", req, cb);
    }
    async DescribeBaselineFixList(req, cb) {
        return this.request("DescribeBaselineFixList", req, cb);
    }
    async DescribeWarningHostConfig(req, cb) {
        return this.request("DescribeWarningHostConfig", req, cb);
    }
    async ModifyMachineAutoClearConfig(req, cb) {
        return this.request("ModifyMachineAutoClearConfig", req, cb);
    }
    async ScanVulAgain(req, cb) {
        return this.request("ScanVulAgain", req, cb);
    }
    async DescribeWebPageServiceInfo(req, cb) {
        return this.request("DescribeWebPageServiceInfo", req, cb);
    }
    async DescribeVulDefenceEvent(req, cb) {
        return this.request("DescribeVulDefenceEvent", req, cb);
    }
    async ModifyRiskDnsPolicyStatus(req, cb) {
        return this.request("ModifyRiskDnsPolicyStatus", req, cb);
    }
    async DescribeVulList(req, cb) {
        return this.request("DescribeVulList", req, cb);
    }
    async ModifyWebHookRuleStatus(req, cb) {
        return this.request("ModifyWebHookRuleStatus", req, cb);
    }
    async ExportBaselineList(req, cb) {
        return this.request("ExportBaselineList", req, cb);
    }
    async DescribeFileTamperRuleInfo(req, cb) {
        return this.request("DescribeFileTamperRuleInfo", req, cb);
    }
    async ScanBaseline(req, cb) {
        return this.request("ScanBaseline", req, cb);
    }
    async ModifyLogStorageConfig(req, cb) {
        return this.request("ModifyLogStorageConfig", req, cb);
    }
    async ModifyFileTamperRule(req, cb) {
        return this.request("ModifyFileTamperRule", req, cb);
    }
    async DescribeMalwareFile(req, cb) {
        return this.request("DescribeMalwareFile", req, cb);
    }
    async DescribeRiskDnsEventList(req, cb) {
        return this.request("DescribeRiskDnsEventList", req, cb);
    }
    async DeletePrivilegeEvents(req, cb) {
        return this.request("DeletePrivilegeEvents", req, cb);
    }
    async DescribeWebHookPolicy(req, cb) {
        return this.request("DescribeWebHookPolicy", req, cb);
    }
    async ModifyRansomDefenseEventsStatus(req, cb) {
        return this.request("ModifyRansomDefenseEventsStatus", req, cb);
    }
    async DescribeAssetPlanTaskList(req, cb) {
        return this.request("DescribeAssetPlanTaskList", req, cb);
    }
    async DescribeAttackEvents(req, cb) {
        return this.request("DescribeAttackEvents", req, cb);
    }
    async DescribeAssetWebAppPluginList(req, cb) {
        return this.request("DescribeAssetWebAppPluginList", req, cb);
    }
    async DescribeLoginWhiteHostList(req, cb) {
        return this.request("DescribeLoginWhiteHostList", req, cb);
    }
    async DescribeSearchLogs(req, cb) {
        return this.request("DescribeSearchLogs", req, cb);
    }
    async DescribeBaselineTop(req, cb) {
        return this.request("DescribeBaselineTop", req, cb);
    }
    async DescribeFastAnalysis(req, cb) {
        return this.request("DescribeFastAnalysis", req, cb);
    }
    async DescribeTrialReport(req, cb) {
        return this.request("DescribeTrialReport", req, cb);
    }
    async DescribeUsualLoginPlaces(req, cb) {
        return this.request("DescribeUsualLoginPlaces", req, cb);
    }
    async ExportFileTamperEvents(req, cb) {
        return this.request("ExportFileTamperEvents", req, cb);
    }
    async AddLoginWhiteLists(req, cb) {
        return this.request("AddLoginWhiteLists", req, cb);
    }
    async ExportProtectDirList(req, cb) {
        return this.request("ExportProtectDirList", req, cb);
    }
    async ExportTasks(req, cb) {
        return this.request("ExportTasks", req, cb);
    }
    async ExportIgnoreRuleEffectHostList(req, cb) {
        return this.request("ExportIgnoreRuleEffectHostList", req, cb);
    }
    async DescribeRiskDnsInfo(req, cb) {
        return this.request("DescribeRiskDnsInfo", req, cb);
    }
    async ExportRansomDefenseEventsList(req, cb) {
        return this.request("ExportRansomDefenseEventsList", req, cb);
    }
    async ModifyWebHookRule(req, cb) {
        return this.request("ModifyWebHookRule", req, cb);
    }
    async DescribeVulDefencePluginExceptionCount(req, cb) {
        return this.request("DescribeVulDefencePluginExceptionCount", req, cb);
    }
    async DescribeIgnoreRuleEffectHostList(req, cb) {
        return this.request("DescribeIgnoreRuleEffectHostList", req, cb);
    }
    async DescribeAssetAppList(req, cb) {
        return this.request("DescribeAssetAppList", req, cb);
    }
    async DescribeLogKafkaDeliverInfo(req, cb) {
        return this.request("DescribeLogKafkaDeliverInfo", req, cb);
    }
    async DescribeESAggregations(req, cb) {
        return this.request("DescribeESAggregations", req, cb);
    }
    async CreateSearchLog(req, cb) {
        return this.request("CreateSearchLog", req, cb);
    }
    async DeleteWebHookRule(req, cb) {
        return this.request("DeleteWebHookRule", req, cb);
    }
    async CreateSearchTemplate(req, cb) {
        return this.request("CreateSearchTemplate", req, cb);
    }
    async ExportIgnoreBaselineRule(req, cb) {
        return this.request("ExportIgnoreBaselineRule", req, cb);
    }
    async DescribePrivilegeEventInfo(req, cb) {
        return this.request("DescribePrivilegeEventInfo", req, cb);
    }
    async DescribeMalwareRiskWarning(req, cb) {
        return this.request("DescribeMalwareRiskWarning", req, cb);
    }
    async DescribeBaselineDefaultStrategyList(req, cb) {
        return this.request("DescribeBaselineDefaultStrategyList", req, cb);
    }
    async CreateVulFix(req, cb) {
        return this.request("CreateVulFix", req, cb);
    }
    async DescribeTagMachines(req, cb) {
        return this.request("DescribeTagMachines", req, cb);
    }
    async EditPrivilegeRules(req, cb) {
        return this.request("EditPrivilegeRules", req, cb);
    }
    async DescribeRiskProcessEvents(req, cb) {
        return this.request("DescribeRiskProcessEvents", req, cb);
    }
    async DescribeAvailableExpertServiceDetail(req, cb) {
        return this.request("DescribeAvailableExpertServiceDetail", req, cb);
    }
    async DescribeEventByTable(req, cb) {
        return this.request("DescribeEventByTable", req, cb);
    }
    async DeleteLicenseRecord(req, cb) {
        return this.request("DeleteLicenseRecord", req, cb);
    }
    async DescribeSecurityDynamics(req, cb) {
        return this.request("DescribeSecurityDynamics", req, cb);
    }
    async DescribeSecurityEventStat(req, cb) {
        return this.request("DescribeSecurityEventStat", req, cb);
    }
    async DescribeAssetDatabaseCount(req, cb) {
        return this.request("DescribeAssetDatabaseCount", req, cb);
    }
    async DescribeLoginWhiteList(req, cb) {
        return this.request("DescribeLoginWhiteList", req, cb);
    }
    async DescribeLogHistogram(req, cb) {
        return this.request("DescribeLogHistogram", req, cb);
    }
    async DescribeMachineFileTamperRules(req, cb) {
        return this.request("DescribeMachineFileTamperRules", req, cb);
    }
    async ModifyLicenseOrder(req, cb) {
        return this.request("ModifyLicenseOrder", req, cb);
    }
    async DescribePublicProxyInstallCommand(req, cb) {
        return this.request("DescribePublicProxyInstallCommand", req, cb);
    }
    async DescribeVulLevelCount(req, cb) {
        return this.request("DescribeVulLevelCount", req, cb);
    }
    async ExportAssetUserList(req, cb) {
        return this.request("ExportAssetUserList", req, cb);
    }
    async DescribeMachineList(req, cb) {
        return this.request("DescribeMachineList", req, cb);
    }
    async DescribeHostLoginList(req, cb) {
        return this.request("DescribeHostLoginList", req, cb);
    }
    async UpdateBaselineStrategy(req, cb) {
        return this.request("UpdateBaselineStrategy", req, cb);
    }
    async CancelIgnoreVul(req, cb) {
        return this.request("CancelIgnoreVul", req, cb);
    }
    async DescribeMalWareList(req, cb) {
        return this.request("DescribeMalWareList", req, cb);
    }
    async DescribeOpenPortStatistics(req, cb) {
        return this.request("DescribeOpenPortStatistics", req, cb);
    }
    async ModifyBanMode(req, cb) {
        return this.request("ModifyBanMode", req, cb);
    }
    async DescribeDirectConnectInstallCommand(req, cb) {
        return this.request("DescribeDirectConnectInstallCommand", req, cb);
    }
    async DescribeWebHookRules(req, cb) {
        return this.request("DescribeWebHookRules", req, cb);
    }
    async DescribeBaselinePolicyList(req, cb) {
        return this.request("DescribeBaselinePolicyList", req, cb);
    }
    async DescribeAssetWebAppCount(req, cb) {
        return this.request("DescribeAssetWebAppCount", req, cb);
    }
    async ModifyLogKafkaDeliverType(req, cb) {
        return this.request("ModifyLogKafkaDeliverType", req, cb);
    }
    async DescribeBashEventsInfo(req, cb) {
        return this.request("DescribeBashEventsInfo", req, cb);
    }
    async DescribeAlarmVertexId(req, cb) {
        return this.request("DescribeAlarmVertexId", req, cb);
    }
    async DescribeVulCveIdInfo(req, cb) {
        return this.request("DescribeVulCveIdInfo", req, cb);
    }
    async ExportRansomDefenseBackupList(req, cb) {
        return this.request("ExportRansomDefenseBackupList", req, cb);
    }
    async DescribeAttackStatistics(req, cb) {
        return this.request("DescribeAttackStatistics", req, cb);
    }
    async DescribeAssetUserKeyList(req, cb) {
        return this.request("DescribeAssetUserKeyList", req, cb);
    }
    async DescribeScanMalwareSchedule(req, cb) {
        return this.request("DescribeScanMalwareSchedule", req, cb);
    }
    async ModifyMalwareWhiteList(req, cb) {
        return this.request("ModifyMalwareWhiteList", req, cb);
    }
    async DescribeWebHookRule(req, cb) {
        return this.request("DescribeWebHookRule", req, cb);
    }
    async RetryVulFix(req, cb) {
        return this.request("RetryVulFix", req, cb);
    }
    async DeleteBashRules(req, cb) {
        return this.request("DeleteBashRules", req, cb);
    }
    async ModifyRaspRules(req, cb) {
        return this.request("ModifyRaspRules", req, cb);
    }
    async RetryCreateSnapshot(req, cb) {
        return this.request("RetryCreateSnapshot", req, cb);
    }
    async DescribeAssetRecentMachineInfo(req, cb) {
        return this.request("DescribeAssetRecentMachineInfo", req, cb);
    }
    async DescribeVulEffectModules(req, cb) {
        return this.request("DescribeVulEffectModules", req, cb);
    }
    async DescribeVulTop(req, cb) {
        return this.request("DescribeVulTop", req, cb);
    }
    async DescribeBruteAttackRules(req, cb) {
        return this.request("DescribeBruteAttackRules", req, cb);
    }
    async DescribeAssetEnvList(req, cb) {
        return this.request("DescribeAssetEnvList", req, cb);
    }
    async DescribeSecurityEventsCnt(req, cb) {
        return this.request("DescribeSecurityEventsCnt", req, cb);
    }
    async DeleteMalwareWhiteList(req, cb) {
        return this.request("DeleteMalwareWhiteList", req, cb);
    }
    async SyncBaselineDetectSummary(req, cb) {
        return this.request("SyncBaselineDetectSummary", req, cb);
    }
    async DescribeLicenseBindSchedule(req, cb) {
        return this.request("DescribeLicenseBindSchedule", req, cb);
    }
    async ModifyNetAttackWhiteList(req, cb) {
        return this.request("ModifyNetAttackWhiteList", req, cb);
    }
    async DescribeWebPageProtectStat(req, cb) {
        return this.request("DescribeWebPageProtectStat", req, cb);
    }
    async DescribeHistoryAccounts(req, cb) {
        return this.request("DescribeHistoryAccounts", req, cb);
    }
    async CreateProtectServer(req, cb) {
        return this.request("CreateProtectServer", req, cb);
    }
    async DescribeBaselineList(req, cb) {
        return this.request("DescribeBaselineList", req, cb);
    }
    async DescribeAssetMachineDetail(req, cb) {
        return this.request("DescribeAssetMachineDetail", req, cb);
    }
    async DescribeVulHostTop(req, cb) {
        return this.request("DescribeVulHostTop", req, cb);
    }
    async ModifyOrderAttribute(req, cb) {
        return this.request("ModifyOrderAttribute", req, cb);
    }
    async DescribeABTestConfig(req, cb) {
        return this.request("DescribeABTestConfig", req, cb);
    }
    async DescribeAssetWebFrameList(req, cb) {
        return this.request("DescribeAssetWebFrameList", req, cb);
    }
    async DescribeAssetCoreModuleList(req, cb) {
        return this.request("DescribeAssetCoreModuleList", req, cb);
    }
    async ModifyMalwareTimingScanSettings(req, cb) {
        return this.request("ModifyMalwareTimingScanSettings", req, cb);
    }
    async CreateMaliciousRequestWhiteList(req, cb) {
        return this.request("CreateMaliciousRequestWhiteList", req, cb);
    }
    async DescribeAssetWebServiceCount(req, cb) {
        return this.request("DescribeAssetWebServiceCount", req, cb);
    }
    async ModifyBaselineWeakPassword(req, cb) {
        return this.request("ModifyBaselineWeakPassword", req, cb);
    }
    async DescribeScreenDefenseTrends(req, cb) {
        return this.request("DescribeScreenDefenseTrends", req, cb);
    }
    async SetBashEventsStatus(req, cb) {
        return this.request("SetBashEventsStatus", req, cb);
    }
    async ChangeStrategyEnableStatus(req, cb) {
        return this.request("ChangeStrategyEnableStatus", req, cb);
    }
    async DescribeProtectDirList(req, cb) {
        return this.request("DescribeProtectDirList", req, cb);
    }
    async DescribeExpertServiceOrderList(req, cb) {
        return this.request("DescribeExpertServiceOrderList", req, cb);
    }
    async CheckLogKafkaConnectionState(req, cb) {
        return this.request("CheckLogKafkaConnectionState", req, cb);
    }
    async DescribeVulDefencePluginStatus(req, cb) {
        return this.request("DescribeVulDefencePluginStatus", req, cb);
    }
    async DescribeTags(req, cb) {
        return this.request("DescribeTags", req, cb);
    }
    async DescribeVulDefencePluginDetail(req, cb) {
        return this.request("DescribeVulDefencePluginDetail", req, cb);
    }
    async ModifyBaselineRule(req, cb) {
        return this.request("ModifyBaselineRule", req, cb);
    }
    async ModifyWebPageProtectSwitch(req, cb) {
        return this.request("ModifyWebPageProtectSwitch", req, cb);
    }
    async ModifyVulDefenceEventStatus(req, cb) {
        return this.request("ModifyVulDefenceEventStatus", req, cb);
    }
    async GetLocalStorageItem(req, cb) {
        return this.request("GetLocalStorageItem", req, cb);
    }
    async ExportRansomDefenseMachineList(req, cb) {
        return this.request("ExportRansomDefenseMachineList", req, cb);
    }
    async DescribeRansomDefenseEventsList(req, cb) {
        return this.request("DescribeRansomDefenseEventsList", req, cb);
    }
    async ExportAssetProcessInfoList(req, cb) {
        return this.request("ExportAssetProcessInfoList", req, cb);
    }
    async DescribeRansomDefenseMachineStrategyInfo(req, cb) {
        return this.request("DescribeRansomDefenseMachineStrategyInfo", req, cb);
    }
    async DescribeBashEvents(req, cb) {
        return this.request("DescribeBashEvents", req, cb);
    }
    async ExportVulDetectionReport(req, cb) {
        return this.request("ExportVulDetectionReport", req, cb);
    }
    async ModifyWarningHostConfig(req, cb) {
        return this.request("ModifyWarningHostConfig", req, cb);
    }
    async DescribeLicenseList(req, cb) {
        return this.request("DescribeLicenseList", req, cb);
    }
    async StartBaselineDetect(req, cb) {
        return this.request("StartBaselineDetect", req, cb);
    }
    async DeleteTags(req, cb) {
        return this.request("DeleteTags", req, cb);
    }
    async TrustMalwares(req, cb) {
        return this.request("TrustMalwares", req, cb);
    }
    async DeleteBaselineRule(req, cb) {
        return this.request("DeleteBaselineRule", req, cb);
    }
    async DescribeWebHookReceiverUsage(req, cb) {
        return this.request("DescribeWebHookReceiverUsage", req, cb);
    }
    async ModifyMaliciousRequestWhiteList(req, cb) {
        return this.request("ModifyMaliciousRequestWhiteList", req, cb);
    }
    async DeleteBaselineRuleIgnore(req, cb) {
        return this.request("DeleteBaselineRuleIgnore", req, cb);
    }
    async ChangeRuleEventsIgnoreStatus(req, cb) {
        return this.request("ChangeRuleEventsIgnoreStatus", req, cb);
    }
    async RansomDefenseRollback(req, cb) {
        return this.request("RansomDefenseRollback", req, cb);
    }
    async DescribeAssetSystemPackageList(req, cb) {
        return this.request("DescribeAssetSystemPackageList", req, cb);
    }
    async DescribeSecurityBroadcasts(req, cb) {
        return this.request("DescribeSecurityBroadcasts", req, cb);
    }
    async DescribeAssetTypes(req, cb) {
        return this.request("DescribeAssetTypes", req, cb);
    }
    async DescribeVulLabels(req, cb) {
        return this.request("DescribeVulLabels", req, cb);
    }
    async StopBaselineDetect(req, cb) {
        return this.request("StopBaselineDetect", req, cb);
    }
    async DescribeBashRules(req, cb) {
        return this.request("DescribeBashRules", req, cb);
    }
    async DescribeAssetDiskList(req, cb) {
        return this.request("DescribeAssetDiskList", req, cb);
    }
    async ModifyVulDefenceSetting(req, cb) {
        return this.request("ModifyVulDefenceSetting", req, cb);
    }
    async ExportVulDefenceEvent(req, cb) {
        return this.request("ExportVulDefenceEvent", req, cb);
    }
    async SyncAssetScan(req, cb) {
        return this.request("SyncAssetScan", req, cb);
    }
    async DescribeScanState(req, cb) {
        return this.request("DescribeScanState", req, cb);
    }
    async DeleteScanTask(req, cb) {
        return this.request("DeleteScanTask", req, cb);
    }
    async ModifyUsersConfig(req, cb) {
        return this.request("ModifyUsersConfig", req, cb);
    }
    async ModifyWebHookPolicy(req, cb) {
        return this.request("ModifyWebHookPolicy", req, cb);
    }
    async DeleteAllJavaMemShells(req, cb) {
        return this.request("DeleteAllJavaMemShells", req, cb);
    }
    async DescribeBaselineItemList(req, cb) {
        return this.request("DescribeBaselineItemList", req, cb);
    }
    async DescribeBashPolicies(req, cb) {
        return this.request("DescribeBashPolicies", req, cb);
    }
    async DescribeRansomDefenseStrategyMachines(req, cb) {
        return this.request("DescribeRansomDefenseStrategyMachines", req, cb);
    }
    async DescribeHistoryService(req, cb) {
        return this.request("DescribeHistoryService", req, cb);
    }
    async DescribeVulCountByDates(req, cb) {
        return this.request("DescribeVulCountByDates", req, cb);
    }
    async ExportVulEffectHostList(req, cb) {
        return this.request("ExportVulEffectHostList", req, cb);
    }
    async DescribeVulStoreList(req, cb) {
        return this.request("DescribeVulStoreList", req, cb);
    }
    async DescribeAlarmIncidentNodes(req, cb) {
        return this.request("DescribeAlarmIncidentNodes", req, cb);
    }
    async DescribeScreenEventsCnt(req, cb) {
        return this.request("DescribeScreenEventsCnt", req, cb);
    }
    async DescribeAssetTotalCount(req, cb) {
        return this.request("DescribeAssetTotalCount", req, cb);
    }
    async DescribeHotVulTop(req, cb) {
        return this.request("DescribeHotVulTop", req, cb);
    }
    async DeleteBaselineWeakPassword(req, cb) {
        return this.request("DeleteBaselineWeakPassword", req, cb);
    }
    async DeleteNonlocalLoginPlaces(req, cb) {
        return this.request("DeleteNonlocalLoginPlaces", req, cb);
    }
    async ExportAssetSystemPackageList(req, cb) {
        return this.request("ExportAssetSystemPackageList", req, cb);
    }
    async EditReverseShellRules(req, cb) {
        return this.request("EditReverseShellRules", req, cb);
    }
    async DeleteWebHookPolicy(req, cb) {
        return this.request("DeleteWebHookPolicy", req, cb);
    }
    async ExportPrivilegeEvents(req, cb) {
        return this.request("ExportPrivilegeEvents", req, cb);
    }
    async ModifyRiskEventsStatus(req, cb) {
        return this.request("ModifyRiskEventsStatus", req, cb);
    }
    async DescribeAssetUserInfo(req, cb) {
        return this.request("DescribeAssetUserInfo", req, cb);
    }
    async DeleteRaspRules(req, cb) {
        return this.request("DeleteRaspRules", req, cb);
    }
    async DeleteMalwares(req, cb) {
        return this.request("DeleteMalwares", req, cb);
    }
    async ExportVulDefencePluginEvent(req, cb) {
        return this.request("ExportVulDefencePluginEvent", req, cb);
    }
    async ScanVulSetting(req, cb) {
        return this.request("ScanVulSetting", req, cb);
    }
    async DescribeAccountStatistics(req, cb) {
        return this.request("DescribeAccountStatistics", req, cb);
    }
    async ExportRiskProcessEvents(req, cb) {
        return this.request("ExportRiskProcessEvents", req, cb);
    }
    async DescribeUndoVulCounts(req, cb) {
        return this.request("DescribeUndoVulCounts", req, cb);
    }
    async DescribeCanNotSeparateMachine(req, cb) {
        return this.request("DescribeCanNotSeparateMachine", req, cb);
    }
    async ExportBruteAttacks(req, cb) {
        return this.request("ExportBruteAttacks", req, cb);
    }
    async ExportAssetEnvList(req, cb) {
        return this.request("ExportAssetEnvList", req, cb);
    }
    async ModifyWebPageProtectDir(req, cb) {
        return this.request("ModifyWebPageProtectDir", req, cb);
    }
    async ExportSecurityTrends(req, cb) {
        return this.request("ExportSecurityTrends", req, cb);
    }
    async ExportMalwares(req, cb) {
        return this.request("ExportMalwares", req, cb);
    }
    async ModifyFileTamperRuleStatus(req, cb) {
        return this.request("ModifyFileTamperRuleStatus", req, cb);
    }
    async ModifyBanWhiteList(req, cb) {
        return this.request("ModifyBanWhiteList", req, cb);
    }
    async ExportWebPageEventList(req, cb) {
        return this.request("ExportWebPageEventList", req, cb);
    }
    async DescribeBaselineHostRiskTop(req, cb) {
        return this.request("DescribeBaselineHostRiskTop", req, cb);
    }
    async ExportRiskDnsEventList(req, cb) {
        return this.request("ExportRiskDnsEventList", req, cb);
    }
    async ExportLicenseDetail(req, cb) {
        return this.request("ExportLicenseDetail", req, cb);
    }
    async DescribeEmergencyResponseList(req, cb) {
        return this.request("DescribeEmergencyResponseList", req, cb);
    }
    async DescribeAttackTrends(req, cb) {
        return this.request("DescribeAttackTrends", req, cb);
    }
    async CreateRansomDefenseStrategy(req, cb) {
        return this.request("CreateRansomDefenseStrategy", req, cb);
    }
    async DescribeScreenEmergentMsg(req, cb) {
        return this.request("DescribeScreenEmergentMsg", req, cb);
    }
    async StopNoticeBanTips(req, cb) {
        return this.request("StopNoticeBanTips", req, cb);
    }
    async DescribeBaselineRule(req, cb) {
        return this.request("DescribeBaselineRule", req, cb);
    }
    async ExportBaselineEffectHostList(req, cb) {
        return this.request("ExportBaselineEffectHostList", req, cb);
    }
    async DescribeFileTamperEvents(req, cb) {
        return this.request("DescribeFileTamperEvents", req, cb);
    }
    async ModifyLicenseBinds(req, cb) {
        return this.request("ModifyLicenseBinds", req, cb);
    }
    async DescribeBanStatus(req, cb) {
        return this.request("DescribeBanStatus", req, cb);
    }
    async DescribeVulDefenceList(req, cb) {
        return this.request("DescribeVulDefenceList", req, cb);
    }
    async DescribeProcessStatistics(req, cb) {
        return this.request("DescribeProcessStatistics", req, cb);
    }
    async DescribeMalwareInfo(req, cb) {
        return this.request("DescribeMalwareInfo", req, cb);
    }
    async DescribeRansomDefenseState(req, cb) {
        return this.request("DescribeRansomDefenseState", req, cb);
    }
    async ExportRiskDnsPolicyList(req, cb) {
        return this.request("ExportRiskDnsPolicyList", req, cb);
    }
    async DescribeStrategyExist(req, cb) {
        return this.request("DescribeStrategyExist", req, cb);
    }
    async CreateBuyBindTask(req, cb) {
        return this.request("CreateBuyBindTask", req, cb);
    }
    async SearchLog(req, cb) {
        return this.request("SearchLog", req, cb);
    }
    async DescribeFileTamperRuleCount(req, cb) {
        return this.request("DescribeFileTamperRuleCount", req, cb);
    }
    async DescribeJavaMemShellPluginList(req, cb) {
        return this.request("DescribeJavaMemShellPluginList", req, cb);
    }
    async CreateEmergencyVulScan(req, cb) {
        return this.request("CreateEmergencyVulScan", req, cb);
    }
    async DescribeProductStatus(req, cb) {
        return this.request("DescribeProductStatus", req, cb);
    }
    async DescribeReverseShellEventInfo(req, cb) {
        return this.request("DescribeReverseShellEventInfo", req, cb);
    }
    async DescribeBanWhiteList(req, cb) {
        return this.request("DescribeBanWhiteList", req, cb);
    }
    async ScanVul(req, cb) {
        return this.request("ScanVul", req, cb);
    }
    async DescribeAttackVulTypeList(req, cb) {
        return this.request("DescribeAttackVulTypeList", req, cb);
    }
    async DescribeRaspMaxCpu(req, cb) {
        return this.request("DescribeRaspMaxCpu", req, cb);
    }
    async DescribeBaselineDetail(req, cb) {
        return this.request("DescribeBaselineDetail", req, cb);
    }
    async DescribeAssetPortCount(req, cb) {
        return this.request("DescribeAssetPortCount", req, cb);
    }
    async DescribeRiskDnsList(req, cb) {
        return this.request("DescribeRiskDnsList", req, cb);
    }
    async DescribeVdbAndPocInfo(req, cb) {
        return this.request("DescribeVdbAndPocInfo", req, cb);
    }
    async ModifyBruteAttackRules(req, cb) {
        return this.request("ModifyBruteAttackRules", req, cb);
    }
    async DescribeMachineOsList(req, cb) {
        return this.request("DescribeMachineOsList", req, cb);
    }
    async DescribeVulEmergentMsg(req, cb) {
        return this.request("DescribeVulEmergentMsg", req, cb);
    }
    async DescribeLicenseGeneral(req, cb) {
        return this.request("DescribeLicenseGeneral", req, cb);
    }
    async DescribeRansomDefenseRollBackTaskList(req, cb) {
        return this.request("DescribeRansomDefenseRollBackTaskList", req, cb);
    }
    async ExportAssetRecentMachineInfo(req, cb) {
        return this.request("ExportAssetRecentMachineInfo", req, cb);
    }
    async ExportReverseShellEvents(req, cb) {
        return this.request("ExportReverseShellEvents", req, cb);
    }
    async CreateNetAttackWhiteList(req, cb) {
        return this.request("CreateNetAttackWhiteList", req, cb);
    }
    async ModifyRiskDnsPolicy(req, cb) {
        return this.request("ModifyRiskDnsPolicy", req, cb);
    }
    async DescribeExpertServiceList(req, cb) {
        return this.request("DescribeExpertServiceList", req, cb);
    }
    async DescribeBaselineItemIgnoreList(req, cb) {
        return this.request("DescribeBaselineItemIgnoreList", req, cb);
    }
    async ExportRansomDefenseStrategyList(req, cb) {
        return this.request("ExportRansomDefenseStrategyList", req, cb);
    }
    async DescribeVulFixStatus(req, cb) {
        return this.request("DescribeVulFixStatus", req, cb);
    }
    async DescribeClientException(req, cb) {
        return this.request("DescribeClientException", req, cb);
    }
    async DescribeBaselineRuleCategoryList(req, cb) {
        return this.request("DescribeBaselineRuleCategoryList", req, cb);
    }
    async DescribeSecurityTrends(req, cb) {
        return this.request("DescribeSecurityTrends", req, cb);
    }
    async DescribeBaselineAnalysisData(req, cb) {
        return this.request("DescribeBaselineAnalysisData", req, cb);
    }
    async DescribeRansomDefenseMachineList(req, cb) {
        return this.request("DescribeRansomDefenseMachineList", req, cb);
    }
    async EditBashRules(req, cb) {
        return this.request("EditBashRules", req, cb);
    }
    async DescribeMonthInspectionReport(req, cb) {
        return this.request("DescribeMonthInspectionReport", req, cb);
    }
    async DescribeAssetInfo(req, cb) {
        return this.request("DescribeAssetInfo", req, cb);
    }
    async DescribeMalwareWhiteListAffectList(req, cb) {
        return this.request("DescribeMalwareWhiteListAffectList", req, cb);
    }
    async IgnoreImpactedHosts(req, cb) {
        return this.request("IgnoreImpactedHosts", req, cb);
    }
    async ExportVulList(req, cb) {
        return this.request("ExportVulList", req, cb);
    }
    async ModifyJavaMemShellsStatus(req, cb) {
        return this.request("ModifyJavaMemShellsStatus", req, cb);
    }
    async ExportAssetPlanTaskList(req, cb) {
        return this.request("ExportAssetPlanTaskList", req, cb);
    }
    async DescribeWebPageEventList(req, cb) {
        return this.request("DescribeWebPageEventList", req, cb);
    }
    async DescribeAgentInstallationToken(req, cb) {
        return this.request("DescribeAgentInstallationToken", req, cb);
    }
    async DescribeAssetMachineTagTop(req, cb) {
        return this.request("DescribeAssetMachineTagTop", req, cb);
    }
}
