import { AbstractClient } from "../../../common/abstract_client";
export class Client extends AbstractClient {
    constructor(clientConfig) {
        super("chc.tencentcloudapi.com", "2023-04-18", clientConfig);
    }
    async CreateQuitWorkOrder(req, cb) {
        return this.request("CreateQuitWorkOrder", req, cb);
    }
    async DescribeWorkOrderTypes(req, cb) {
        return this.request("DescribeWorkOrderTypes", req, cb);
    }
    async DescribeModel(req, cb) {
        return this.request("DescribeModel", req, cb);
    }
    async DescribeRacks(req, cb) {
        return this.request("DescribeRacks", req, cb);
    }
    async CreateModelEvaluationWorkOrder(req, cb) {
        return this.request("CreateModelEvaluationWorkOrder", req, cb);
    }
    async CreateServerModel(req, cb) {
        return this.request("CreateServerModel", req, cb);
    }
    async CreateRackOffWorkOrder(req, cb) {
        return this.request("CreateRackOffWorkOrder", req, cb);
    }
    async DescribeModelVersionList(req, cb) {
        return this.request("DescribeModelVersionList", req, cb);
    }
    async DescribeCommonServiceWorkOrderDetail(req, cb) {
        return this.request("DescribeCommonServiceWorkOrderDetail", req, cb);
    }
    async ModifyWorkOrderTypeCollectFlag(req, cb) {
        return this.request("ModifyWorkOrderTypeCollectFlag", req, cb);
    }
    async DescribeModelEvaluationWorkOrderDetail(req, cb) {
        return this.request("DescribeModelEvaluationWorkOrderDetail", req, cb);
    }
    async DescribeCustomerInfo(req, cb) {
        return this.request("DescribeCustomerInfo", req, cb);
    }
    async DescribeCampusList(req, cb) {
        return this.request("DescribeCampusList", req, cb);
    }
    async DescribeRacksDistribution(req, cb) {
        return this.request("DescribeRacksDistribution", req, cb);
    }
    async DescribePositions(req, cb) {
        return this.request("DescribePositions", req, cb);
    }
    async CreateCommonServiceWorkOrder(req, cb) {
        return this.request("CreateCommonServiceWorkOrder", req, cb);
    }
    async DescribeDeviceWorkOrderDetail(req, cb) {
        return this.request("DescribeDeviceWorkOrderDetail", req, cb);
    }
    async DescribeAvailableModelList(req, cb) {
        return this.request("DescribeAvailableModelList", req, cb);
    }
    async DescribeDeviceList(req, cb) {
        return this.request("DescribeDeviceList", req, cb);
    }
    async CreatePowerOnWorkOrder(req, cb) {
        return this.request("CreatePowerOnWorkOrder", req, cb);
    }
    async DescribePositionStatusSummary(req, cb) {
        return this.request("DescribePositionStatusSummary", req, cb);
    }
    async ExportCustomerWorkOrderDetail(req, cb) {
        return this.request("ExportCustomerWorkOrderDetail", req, cb);
    }
    async CreateSpeciallyQuitWorkOrder(req, cb) {
        return this.request("CreateSpeciallyQuitWorkOrder", req, cb);
    }
    async CreatePowerOffWorkOrder(req, cb) {
        return this.request("CreatePowerOffWorkOrder", req, cb);
    }
    async CreatePersonnelVisitWorkOrder(req, cb) {
        return this.request("CreatePersonnelVisitWorkOrder", req, cb);
    }
    async DescribePersonnelVisitWorkOrderDetail(req, cb) {
        return this.request("DescribePersonnelVisitWorkOrderDetail", req, cb);
    }
    async DescribeIdcUnitDetail(req, cb) {
        return this.request("DescribeIdcUnitDetail", req, cb);
    }
    async ConfirmCommonServiceWorkOrder(req, cb) {
        return this.request("ConfirmCommonServiceWorkOrder", req, cb);
    }
    async CreateMovingWorkOrder(req, cb) {
        return this.request("CreateMovingWorkOrder", req, cb);
    }
    async CreateNetDeviceModel(req, cb) {
        return this.request("CreateNetDeviceModel", req, cb);
    }
    async DescribeWorkOrderStatistics(req, cb) {
        return this.request("DescribeWorkOrderStatistics", req, cb);
    }
    async DescribeResourceUsage(req, cb) {
        return this.request("DescribeResourceUsage", req, cb);
    }
    async DescribeIdcUnitAssetDetail(req, cb) {
        return this.request("DescribeIdcUnitAssetDetail", req, cb);
    }
    async CreateRackOnWorkOrder(req, cb) {
        return this.request("CreateRackOnWorkOrder", req, cb);
    }
    async DescribeIdcs(req, cb) {
        return this.request("DescribeIdcs", req, cb);
    }
    async CreateReceivingWorkOrder(req, cb) {
        return this.request("CreateReceivingWorkOrder", req, cb);
    }
    async DescribeModelTemplate(req, cb) {
        return this.request("DescribeModelTemplate", req, cb);
    }
    async DescribeWorkOrderList(req, cb) {
        return this.request("DescribeWorkOrderList", req, cb);
    }
}
