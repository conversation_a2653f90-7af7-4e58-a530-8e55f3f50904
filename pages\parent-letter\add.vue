<template>
    <view class="uni-container">
        <uni-forms ref="form" :modelValue="formData" :rules="rules" validateTrigger="bind">

            <view class="section-content">
                <uni-forms-item label="家长信标题" name="title" required>
                    <uni-easyinput v-model="formData.title" placeholder="请输入家长信标题" :maxlength="100" trim="both" />
                </uni-forms-item>

                <uni-forms-item label="家长信类型" name="type">
                    <uni-data-select v-model="formData.type" :localdata="typeOptions" placeholder="请选择类型（选填）" />
                </uni-forms-item>

                <!-- 级别选择组件 - 改为选填 -->
                <uni-forms-item label="教材级别" name="level" :required="false">
                    <view class="level-tags-container">
                        <view class="level-tags-header">
                            <text class="level-title">可选级别</text>
                        </view>
                        <view class="level-tags-list">
                            <view v-for="level in levelOptions" :key="level" class="level-tag"
                                :class="{ selected: selectedLevel === level }" @click="handleLevelChange(level)">
                                {{ level }}
                            </view>
                        </view>
                        <view v-if="selectedLevel" class="selected-level">
                            <text class="selected-level-label">已选级别：</text>
                            <view class="selected-level-tag">
                                <text>{{ selectedLevel }}</text>
                                <text class="remove-btn" @click="handleLevelChange('')">×</text>
                            </view>
                        </view>
                    </view>
                </uni-forms-item>

                <!-- 标签管理组件 -->
                <uni-forms-item label="标签" name="tags">
                    <tag-manager :selected-tags="formData.tags" :show-label="false" :max-tags="5"
                        @tags-change="handleTagsChange" />
                </uni-forms-item>
            </view>


            <view class="section-content">
                <!-- 班级选择 - 改为多选 -->
                <uni-forms-item label="发布班级" name="class_ids" required>
                    <view class="class-selector">
                        <checkbox-group @change="handleClassChange">
                            <label v-for="item in classList" :key="item._id" class="class-item">
                                <checkbox :value="item._id" :checked="formData.class_ids.includes(item._id)" />
                                <text class="class-name">{{ item.name }}</text>
                            </label>
                        </checkbox-group>
                        <view v-if="classList.length === 0" class="empty-tip">暂无可选班级</view>
                    </view>
                </uni-forms-item>

                <!-- 学生选择 -->
                <uni-forms-item label="指定学生" name="recipients.student_ids">
                    <view class="student-selector">
                        <view class="tip-message">
                            <text>不选择学生则默认发送给所选班级的全部学生</text>
                        </view>
                        <checkbox-group @change="handleStudentChange">
                            <label v-for="item in filteredStudentList" :key="item._id" class="student-item">
                                <checkbox :value="item._id"
                                    :checked="formData.recipients.student_ids.includes(item._id)" />
                                <text class="student-name">{{ item.name }}</text>
                            </label>
                        </checkbox-group>
                        <view v-if="filteredStudentList.length === 0" class="empty-tip">
                            {{ formData.class_ids.length > 0 ? '所选班级暂无学生' : '请先选择班级' }}
                        </view>
                    </view>
                </uni-forms-item>
            </view>

            <!-- PDF文件上传 -->
            <view class="section-content">
                <uni-forms-item label="PDF文件" name="pdf_files" required>
                    <view class="pdf-upload-section">
                        <!-- 上传按钮区域 - 与作业页面多媒体样式保持一致 -->
                        <view class="upload-buttons">
                            <view class="upload-btn" @click="selectCreationMethod('upload')">
                                <uni-icons type="cloud-upload" size="18" color="#409eff"></uni-icons>
                                <text class="btn-text">本地上传</text>
                            </view>
                            <view class="upload-btn" @click="selectCreationMethod('template')">
                                <uni-icons type="folder" size="18" color="#67c23a"></uni-icons>
                                <text class="btn-text">选择模板</text>
                            </view>
                        </view>

                        <!-- PDF预览区域 - 支持多文件 -->
                        <view v-if="formData.pdf_files && formData.pdf_files.length > 0" class="pdf-preview-area">
                            <view class="preview-grid">
                                <view v-for="(pdfFile, index) in (formData.pdf_files || [])" :key="index"
                                    class="preview-item">
                                    <view class="preview-content" @click="previewPDF(pdfFile, index)">
                                        <view class="file-icon">
                                            <uni-icons type="file-pdf" size="40" color="#DD524D"></uni-icons>
                                        </view>
                                        <text class="file-name">{{ getShortFileName(pdfFile.file_name) }}</text>

                                        <!-- 文件类型标识 -->
                                        <view class="file-type-badge">
                                            <text class="badge-text">PDF</text>
                                        </view>

                                        <!-- 来源标识 -->
                                        <view v-if="pdfFile.source === 'template'" class="template-badge">
                                            <text class="badge-text">模板</text>
                                        </view>
                                        <view v-else-if="pdfFile.isSecondUpload" class="second-upload-badge">
                                            <text class="badge-text">秒传</text>
                                        </view>
                                    </view>

                                    <!-- 删除按钮 -->
                                    <view class="delete-btn" @click.stop="removePDFFile(index)">
                                        <uni-icons type="close" size="12" color="#fff"></uni-icons>
                                    </view>
                                </view>
                            </view>

                            <!-- 🎯 修改：文件信息显示 - 显示不同类型文件数量 -->
                            <view class="file-count">
                                <uni-icons type="checkmarkempty" size="16" color="#67c23a"></uni-icons>
                                <text class="count-text">{{ getFileCountText() }}</text>
                                <button class="clear-btn" type="default" size="mini"
                                    @click="clearAllPDFFiles">清空</button>
                            </view>
                        </view>

                        <!-- 支持格式提示 -->
                        <view class="format-tips">
                            <text class="tips-text">支持PDF格式，可本地上传或从模板库选择</text>
                        </view>
                    </view>
                </uni-forms-item>

                <!-- 隐藏的PDF上传组件 -->
                <pdf-uploader ref="pdfUploader" :campus-id="formData.campus_id" :teacher-id="formData.teacher_id"
                    :level="selectedLevel" :tags="formData.tags" :original-class="getCurrentClassName()"
                    @upload-success="handlePDFUploadSuccess" @upload-error="handlePDFUploadError"
                    @file-removed="handlePDFFileRemoved" style="display: none;" />
            </view>

            <view class="uni-button-group">
                <button style="width: 100px;" type="primary" class="uni-button" @click="sendLetter">提交</button>
                <navigator open-type="navigateBack" style="margin-left: 15px;">
                    <button style="width: 100px;" class="uni-button">返回</button>
                </navigator>
            </view>
        </uni-forms>

        <!-- 模板选择器组件 -->
        <template-selector ref="templateSelector" :campus-id="formData.campus_id" :current-level="formData.level"
            @template-selected="handleTemplateSelected" />
    </view>

    <!-- #ifndef H5 -->
    <fix-window />
    <!-- #endif -->

</template>

<script>
import { rules } from '@/js_sdk/validator/parent-letter.js'
import PDFUploader from '@/components/pdf-uploader/pdf-uploader.vue'
import TemplateSelector from '@/components/template-selector/template-selector.vue'
import TagManager from '@/components/tag-manager/tag-manager.vue'

const db = uniCloud.database()

export default {
    components: {
        PDFUploader,
        TemplateSelector,
        TagManager
    },
    data() {
        return {
            formData: {
                title: '',
                type: '',
                level: '', // 新增：教材级别
                tags: [], // 新增：标签数组
                class_ids: [], // 改为多选班级数组
                teacher_id: '',
                teacher_name: '', // 添加teacher_name字段
                campus_id: '',
                recipients: {
                    type: 'all',
                    student_ids: [],
                    student_count: 0
                },
                content: {
                    creation_method: 'upload', // 默认为上传模式
                    raw_content: '',
                    plain_text: '',
                    template_id: '',
                    template_data: {},
                    uploaded_file: null,
                    attachments: [],
                    images: []
                },
                pdf_files: [], // 新增：PDF文件列表（支持多个文件）
                related_data: {
                    homework_id: ''
                },
                status: 'draft'
            },
            rules,
            loading: false,
            classList: [],
            studentList: [],
            templateList: [],
            templateVariables: [],
            uploadedFiles: [],
            selectedTemplate: null, // 新增：选中的模板
            typeOptions: [
                { value: 'daily', text: '日常反馈' },
                { value: 'weekly', text: '周报总结' },
                { value: 'homework', text: '作业相关' },
                { value: 'notice', text: '通知公告' },
                { value: 'behavior', text: '行为表现' },
                { value: 'achievement', text: '成绩反馈' },
                { value: 'other', text: '其他' }
            ],
            levelOptions: ['G1', 'G2', 'K1', 'K2', 'P1', 'P2', 'P3', 'P4', 'P5', 'P6'],
            selectedLevel: '' // 新增：用于管理级别选择的内部状态
        }
    },

    computed: {
        // 根据选中的班级过滤学生列表 - 支持多选班级
        filteredStudentList() {
            if (!this.formData.class_ids || this.formData.class_ids.length === 0) {
                return [];
            }

            return this.studentList.filter(student => {
                // 检查学生的classes数组中是否包含任一选中的班级
                if (student.classes && Array.isArray(student.classes)) {
                    return student.classes.some(cls =>
                        this.formData.class_ids.includes(cls.class_id)
                    );
                }
                return false;
            });
        }
    },

    watch: {
        // 监听selectedLevel变化，同步到formData
        selectedLevel(val) {
            this.formData.level = val
            // 如果有PDF上传组件，更新其级别属性
            // if (this.$refs.pdfUploader) {
            //     this.$refs.pdfUploader.level = val
            // }
        },

        // 监听formData.level变化，同步到selectedLevel
        'formData.level'(val) {
            if (val !== this.selectedLevel) {
                this.selectedLevel = val
            }
        }
    },

    onLoad() {
        this.initUserInfo()
        this.loadClassList()
        this.loadTemplateList()
        this.loadAllStudents()

        // 确保pdf_files是数组
        if (!this.formData.pdf_files) {
            this.$set(this.formData, 'pdf_files', [])
        }
    },

    created() {
        // 确保pdf_files是数组
        if (!this.formData.pdf_files) {
            this.formData.pdf_files = []
        }
    },

    methods: {
        // 初始化用户信息
        initUserInfo() {
            const userInfo = this.$store.state.user.userInfo
            console.log('当前用户信息:', userInfo)

            if (userInfo) {
                // 设置teacher_id，确保它不为空
                this.formData.teacher_id = userInfo._id

                // 设置teacher_name，为超级管理员添加特殊标识
                let displayName = userInfo.nickname || userInfo.username || '管理员'

                // 检查是否是超级管理员
                if (userInfo.role && (userInfo.role.includes('admin') || userInfo.role.includes('administrator'))) {
                    // 为超级管理员添加标识
                    this.formData.teacher_name = `[管理员] ${displayName}`
                } else {
                    this.formData.teacher_name = displayName
                }

                // 设置campus_id，如果有的话
                this.formData.campus_id = userInfo.campus_id || ''
            } else {
                // 如果没有用户信息，尝试从本地存储获取
                try {
                    const localUserInfo = uni.getStorageSync('uni-id-pages-userInfo')
                    console.log('从本地存储获取用户信息:', localUserInfo)

                    if (localUserInfo && localUserInfo._id) {
                        this.formData.teacher_id = localUserInfo._id

                        // 设置teacher_name，为超级管理员添加特殊标识
                        let displayName = localUserInfo.nickname || localUserInfo.username || '管理员'

                        // 检查是否是超级管理员
                        if (localUserInfo.role && (localUserInfo.role.includes('admin') || localUserInfo.role.includes('administrator'))) {
                            // 为超级管理员添加标识
                            this.formData.teacher_name = `[管理员] ${displayName}`
                        } else {
                            this.formData.teacher_name = displayName
                        }

                        this.formData.campus_id = localUserInfo.campus_id || ''
                    } else {
                        console.warn('无法获取用户信息')
                        uni.showToast({
                            title: '请先登录',
                            icon: 'none'
                        })
                    }
                } catch (e) {
                    console.error('获取本地用户信息失败:', e)
                }
            }

            console.log('初始化后的teacher_id:', this.formData.teacher_id)
            console.log('初始化后的teacher_name:', this.formData.teacher_name)
        },

        // 加载班级列表
        async loadClassList() {
            uni.showLoading({
                title: '加载班级数据...'
            })

            // 获取当前用户信息，如果是教师角色，只加载其任教的班级
            const userInfo = this.$store.state.user.userInfo || {}
            let where = {}

            if (userInfo.role && userInfo.role.includes('teacher') && userInfo._id) {
                // 查询教师任教的班级
                where = {
                    $or: [
                        { head_teacher_id: userInfo._id },
                        { teaching_teachers: userInfo._id },
                        { assistant_teachers: userInfo._id }
                    ]
                }
            }

            console.log('加载班级列表，查询条件:', JSON.stringify(where))

            try {
                const result = await db.collection('class')
                    .where(where)
                    .field('_id,name,campus_id')
                    .orderBy('create_date', 'desc')
                    .get()

                console.log('班级列表加载成功:', JSON.stringify(result.result.data))
                this.classList = result.result.data
            } catch (error) {
                console.error('加载班级列表失败', error)
                uni.showToast({
                    title: '加载班级失败',
                    icon: 'error'
                })
            } finally {
                uni.hideLoading()
            }
        },

        // 加载模板列表
        async loadTemplateList() {
            try {
                const result = await db.collection('parent-letter-template')
                    .where({
                        campus_id: this.formData.campus_id
                    })
                    .field('_id,template_name,template_type')
                    .orderBy('usage_count', 'desc')
                    .get()

                this.templateList = result.result.data.map(item => ({
                    value: item._id,
                    text: item.template_name
                }))
            } catch (error) {
                console.error('加载模板列表失败', error)
            }
        },

        // 加载作业列表
        async loadHomeworkList() {
            try {
                const result = await db.collection('homework')
                    .where({
                        teacher_id: this.formData.teacher_id,
                        status: '已发布'
                    })
                    .field('_id,title')
                    .orderBy('publish_time', 'desc')
                    .limit(20)
                    .get()

                this.homeworkList = result.result.data.map(item => ({
                    value: item._id,
                    text: item.title
                }))
            } catch (error) {
                console.error('加载作业列表失败', error)
            }
        },

        // 加载所有学生数据
        async loadAllStudents() {
            try {
                const result = await db.collection('student')
                    .field('_id,name,campus_id,classes')
                    .orderBy('create_date', 'desc')
                    .get()

                console.log('学生列表加载成功:', JSON.stringify(result.result.data))
                this.studentList = result.result.data
            } catch (error) {
                console.error('加载学生列表失败', error)
                uni.showToast({
                    title: '加载学生失败',
                    icon: 'error'
                })
            }
        },

        // 处理班级点击（支持取消选择）
        handleClassClick(classId) {
            if (this.formData.class_id === classId) {
                // 如果点击的是已选中的班级，则取消选择
                this.formData.class_id = ''
                this.formData.recipients.student_ids = []
                this.formData.recipients.student_count = 0
                this.formData.recipients.type = 'all'
            } else {
                // 选择新班级
                this.formData.class_id = classId
                this.formData.recipients.student_ids = []
                this.formData.recipients.type = 'all'

                // 等待下一个tick，确保filteredStudentList已更新
                this.$nextTick(() => {
                    const classStudents = this.filteredStudentList
                    this.formData.recipients.student_ids = classStudents.map(s => s._id)
                    this.formData.recipients.student_count = classStudents.length
                    console.log('自动选择班级学生:', classStudents.length, '人')
                })
            }
        },

        // 处理班级选择变化 - 支持多选
        handleClassChange(e) {
            console.log('班级选择变化:', e.detail.value)
            this.formData.class_ids = e.detail.value

            // 班级变化后，清空已选学生
            this.formData.recipients.student_ids = []

            // 如果选择了班级，自动设置为全班发送
            if (this.formData.class_ids.length > 0) {
                this.formData.recipients.type = 'all'
                // 等待下一个tick，确保filteredStudentList已更新
                this.$nextTick(() => {
                    const classStudents = this.filteredStudentList
                    this.formData.recipients.student_ids = classStudents.map(s => s._id)
                    this.formData.recipients.student_count = classStudents.length
                    console.log('自动选择班级学生:', classStudents.length, '人')
                })
            } else {
                this.formData.recipients.student_count = 0
            }
        },

        // 处理学生选择变化
        handleStudentChange(e) {
            console.log('学生选择变化:', e.detail.value)
            this.formData.recipients.student_ids = e.detail.value
            this.formData.recipients.student_count = e.detail.value.length

            // 根据选择的学生数量自动设置发送类型
            const totalStudents = this.filteredStudentList.length
            if (e.detail.value.length === 0) {
                // 如果没有选择学生，默认全班发送
                this.formData.recipients.type = 'all'
                this.formData.recipients.student_ids = this.filteredStudentList.map(s => s._id)
                this.formData.recipients.student_count = totalStudents
            } else if (e.detail.value.length === totalStudents) {
                // 如果选择了全部学生，设置为全班发送
                this.formData.recipients.type = 'all'
            } else {
                // 如果只选择了部分学生，设置为指定学生
                this.formData.recipients.type = 'selected'
            }
        },

        // 创建方式变化
        onCreationMethodChange(e) {
            this.creationMethodIndex = e.currentIndex
            const methods = ['custom', 'template', 'upload']
            this.formData.content.creation_method = methods[e.currentIndex]

            // 清空之前的内容
            this.formData.content.raw_content = ''
            this.formData.content.template_id = ''
            this.formData.content.uploaded_file = null
        },

        // 编辑器准备就绪
        onEditorReady() {
            // 检查平台支持
            if (typeof uni.createEditorContext === 'function') {
                this.editorCtx = uni.createEditorContext('letter-editor', this)
            } else {
                console.warn('当前平台不支持 createEditorContext')
            }
        },

        // 内容变化（富文本编辑器）
        onContentChange(e) {
            this.formData.content.raw_content = e.detail.html
            this.formData.content.plain_text = e.detail.text
        },

        // 文本框内容变化（H5平台）
        onTextareaChange(e) {
            this.formData.content.plain_text = e
        },

        // 加载模板
        async loadTemplate(templateId) {
            if (!templateId) return

            try {
                const result = await db.collection('parent-letter-template')
                    .doc(templateId)
                    .get()

                const template = result.result.data[0]
                this.templateVariables = template.variables || []

                // 初始化模板数据
                this.formData.content.template_data = {}
                this.templateVariables.forEach(variable => {
                    this.formData.content.template_data[variable.key] = variable.default_value || ''
                })
            } catch (error) {
                console.error('加载模板失败', error)
                uni.showToast({
                    title: '加载模板失败',
                    icon: 'error'
                })
            }
        },

        // 文件选择
        onFileSelect(e) {
            console.log('文件选择', e)
        },

        // 文件上传成功
        onUploadSuccess(e) {
            if (e.tempFilePaths && e.tempFilePaths.length > 0) {
                this.formData.content.uploaded_file = {
                    file_id: e.tempFilePaths[0],
                    file_name: e.name || '未知文件',
                    file_type: e.extname || '',
                    file_size: e.size || 0
                }
            }
        },

        // 表单验证
        async validateForm() {
            try {
                // 先确保pdf_files是数组
                if (!this.formData.pdf_files) {
                    this.$set(this.formData, 'pdf_files', [])
                }

                // 手动检查pdf_files是否有文件
                if (!this.formData.pdf_files.length) {
                    console.log('PDF文件列表为空')
                    uni.showToast({
                        title: '请上传PDF文件',
                        icon: 'none'
                    })
                    return false
                }

                // 确保所有文件都有source属性
                this.formData.pdf_files.forEach(file => {
                    if (!file.source) {
                        file.source = 'upload'; // 默认为上传来源
                    }
                });

                await this.$refs.form.validate()
                return true
            } catch (error) {
                console.log('表单验证失败', error)

                // 显示具体的错误信息
                if (Array.isArray(error) && error.length > 0) {
                    const firstError = error[0]
                    uni.showToast({
                        title: firstError.errorMessage || '表单验证失败',
                        icon: 'none'
                    })
                } else {
                    uni.showToast({
                        title: '表单验证失败',
                        icon: 'none'
                    })
                }

                return false
            }
        },

        // 保存草稿 - 优化版本，支持PDF信息保存但不入库模板
        async saveDraft() {
            if (this.loading) return

            // 基本验证（草稿不需要完整验证）
            if (!this.formData.title.trim()) {
                uni.showToast({
                    title: '请输入家长信标题',
                    icon: 'error'
                })
                return
            }

            if (!this.formData.class_ids || this.formData.class_ids.length === 0) {
                uni.showToast({
                    title: '请选择班级',
                    icon: 'error'
                })
                return
            }

            this.loading = true

            try {
                // 准备草稿数据 - 包含PDF信息但不触发模板库保存
                const draftData = {
                    ...this.formData,
                    status: 'draft',
                    // 移除create_time和update_time，让uniCloud自动处理
                    // create_time: new Date(),
                    // update_time: new Date()
                }

                // 保存草稿到家长信表
                const result = await db.collection('parent-letter').add(draftData)
                console.log('草稿保存成功:', result)

                // 注意：草稿保存时不会触发模板库入库，只有发送时才会

                uni.showToast({
                    title: '草稿已保存',
                    icon: 'success'
                })

                setTimeout(() => {
                    uni.navigateBack({
                        success: () => {
                            uni.$emit('refreshData')
                        }
                    })
                }, 1500)
            } catch (error) {
                console.error('保存草稿失败', error)
                uni.showToast({
                    title: '保存失败',
                    icon: 'error'
                })
            } finally {
                this.loading = false
            }
        },

        // 新增方法：处理级别变化
        handleLevelChange(level) {
            console.log('级别选择变化:', level)
            // 更新内部状态，而不是直接修改formData
            this.selectedLevel = level
        },

        // 新增方法：处理标签变化
        handleTagsChange(tags) {
            console.log('标签选择变化:', tags)
            // 创建一个新数组来更新formData.tags，避免直接修改props
            this.$set(this.formData, 'tags', Array.isArray(tags) ? [...tags] : [])

            // 如果有PDF上传组件，更新其标签属性
            // if (this.$refs.pdfUploader) {
            //     this.$refs.pdfUploader.tags = Array.isArray(tags) ? [...tags] : []
            // }
        },

        // 新增方法：处理创建方式变化
        handleCreationMethodChange(e) {
            const method = e.detail.value
            console.log('创建方式变化:', method)
            this.selectCreationMethod(method)
        },

        // 🎯 修改：选择创建方式 - 支持文件累加而不是替换
        selectCreationMethod(method) {
            console.log('选择创建方式:', method)

            if (method === 'upload') {
                // 本地上传模式
                this.formData.content.creation_method = 'upload'
                // 注意：不清除已有的PDF文件，支持混合模式
                // 触发PDF上传组件的文件选择
                if (this.$refs.pdfUploader) {
                    this.$refs.pdfUploader.selectFile()
                }
            } else if (method === 'template') {
                // 模板选择模式
                this.formData.content.creation_method = 'template'
                // 🎯 修改：不清空已有的PDF文件，支持添加模板到现有文件列表
                // this.formData.pdf_files = [] // 注释掉这行，保留已有文件
                // 打开模板选择器
                this.openTemplateSelector()
            }
        },

        // 新增方法：获取当前班级名称 - 支持多选班级
        getCurrentClassName() {
            if (!this.formData.class_ids || this.formData.class_ids.length === 0) return ''

            // 如果选择了多个班级，返回班级名称列表
            const selectedClasses = this.classList.filter(cls =>
                this.formData.class_ids.includes(cls._id)
            )

            return selectedClasses.map(cls => cls.name).join('、')
        },

        // 🎯 修改：处理PDF上传成功 - 支持添加到现有文件列表
        handlePDFUploadSuccess(result) {
            console.log('PDF上传成功:', result)

            // 处理不同的返回结构情况
            let fileInfo = null
            if (result.fileInfo) {
                // 直接使用返回的fileInfo
                fileInfo = result.fileInfo
            } else if (result.originalResult && result.originalResult.pdfInfo) {
                // 使用originalResult中的pdfInfo
                fileInfo = result.originalResult.pdfInfo
            } else if (result.pdfInfo) {
                // 直接使用pdfInfo
                fileInfo = result.pdfInfo
            } else {
                console.error('无法从上传结果中获取文件信息')
                uni.showToast({
                    title: '上传结果格式不正确',
                    icon: 'error'
                })
                return
            }

            // 确保文件名以.pdf结尾
            let fileName = fileInfo.fileName || '未命名文件';
            if (!fileName.toLowerCase().endsWith('.pdf')) {
                fileName += '.pdf';
            }

            // 🎯 检查是否已经存在相同的文件，避免重复添加
            const existingFileIndex = this.formData.pdf_files.findIndex(file =>
                file.source === 'upload' &&
                (file.file_id === fileInfo.fileID || file.file_name === fileName)
            );

            if (existingFileIndex !== -1) {
                // 如果已存在相同文件，询问是否替换
                uni.showModal({
                    title: '文件已存在',
                    content: '相同的文件已在列表中，是否替换？',
                    success: (res) => {
                        if (res.confirm) {
                            // 替换现有文件
                            const pdfFile = this.createUploadPDFObject(fileInfo, fileName, result);
                            this.formData.pdf_files.splice(existingFileIndex, 1, pdfFile);
                            uni.showToast({
                                title: '文件已替换',
                                icon: 'success'
                            });
                        }
                    }
                });
                return;
            }

            // 创建PDF文件对象并添加到文件列表
            const pdfFile = this.createUploadPDFObject(fileInfo, fileName, result);
            this.formData.pdf_files.push(pdfFile);

            uni.showToast({
                title: `PDF上传成功 (${this.formData.pdf_files.length}个文件)`,
                icon: 'success'
            });
        },

        // 🎯 新增：创建上传PDF对象的辅助方法
        createUploadPDFObject(fileInfo, fileName, result) {
            return {
                file_id: fileInfo.fileID || '',
                file_name: fileName,
                file_size: typeof fileInfo.fileSize === 'number' ? fileInfo.fileSize : 0,
                cloud_path: fileInfo.cloudPath || '',
                url: fileInfo.url || '',
                source: 'upload', // 标记为本地上传
                isSecondUpload: result.isSecondUpload || fileInfo.isSecondUpload || false,
                template_source: null // 本地上传没有模板来源
            };
        },

        // 🎯 新增：获取文件数量显示文本
        getFileCountText() {
            if (!this.formData.pdf_files || this.formData.pdf_files.length === 0) {
                return '未添加文件';
            }

            const uploadCount = this.formData.pdf_files.filter(file => file.source === 'upload').length;
            const templateCount = this.formData.pdf_files.filter(file => file.source === 'template').length;
            const totalCount = this.formData.pdf_files.length;

            if (uploadCount > 0 && templateCount > 0) {
                return `已添加 ${totalCount} 个文件 (上传${uploadCount}个, 模板${templateCount}个)`;
            } else if (uploadCount > 0) {
                return `已添加 ${uploadCount} 个上传文件`;
            } else if (templateCount > 0) {
                return `已添加 ${templateCount} 个模板文件`;
            } else {
                return `已添加 ${totalCount} 个文件`;
            }
        },

        // 新增方法：处理PDF上传错误
        handlePDFUploadError(error) {
            console.error('PDF上传失败:', error)
            this.formData.pdf_files = [] // 清空PDF文件列表

            uni.showToast({
                title: error.error || 'PDF上传失败',
                icon: 'error'
            })
        },

        // 新增方法：处理PDF文件移除
        handlePDFFileRemoved() {
            console.log('PDF文件已移除')
            this.formData.pdf_files = [] // 清空PDF文件列表
        },

        // 新增方法：打开模板选择器
        openTemplateSelector() {
            console.log('打开模板选择器')
            this.$refs.templateSelector.open()
        },

        // 🎯 修改：处理模板选择 - 支持添加到现有文件列表
        handleTemplateSelected(selectionData) {
            console.log('模板选择完成:', selectionData)

            this.selectedTemplate = selectionData.template

            // 自动填充表单数据（仅在没有标题时填充）
            if (selectionData.useTemplateTitle && !this.formData.title.trim()) {
                this.formData.title = selectionData.templateTitle
            }

            // 更新级别和标签（如果模板有的话且当前为空）
            if (selectionData.template.level && !this.formData.level) {
                this.formData.level = selectionData.template.level
            }
            if (selectionData.template.tags && selectionData.template.tags.length > 0 && this.formData.tags.length === 0) {
                this.formData.tags = [...selectionData.template.tags]
            }

            // 确保文件名以.pdf结尾
            let fileName = selectionData.template.file_name || '未命名文件';
            if (!fileName.toLowerCase().endsWith('.pdf')) {
                fileName += '.pdf';
            }

            // 🎯 检查是否已经存在相同的模板文件，避免重复添加
            const existingTemplateIndex = this.formData.pdf_files.findIndex(file =>
                file.source === 'template' && file.template_id === selectionData.template._id
            );

            if (existingTemplateIndex !== -1) {
                // 如果已存在相同模板，询问是否替换
                uni.showModal({
                    title: '模板已存在',
                    content: '该模板文件已在列表中，是否替换？',
                    success: (res) => {
                        if (res.confirm) {
                            // 替换现有模板
                            const templatePDF = this.createTemplatePDFObject(selectionData, fileName);
                            this.formData.pdf_files.splice(existingTemplateIndex, 1, templatePDF);
                            uni.showToast({
                                title: '模板已替换',
                                icon: 'success'
                            });
                        }
                    }
                });
                return;
            }

            // 创建模板PDF对象并添加到文件列表
            const templatePDF = this.createTemplatePDFObject(selectionData, fileName);
            this.formData.pdf_files.push(templatePDF);

            uni.showToast({
                title: `模板添加成功 (${this.formData.pdf_files.length}个文件)`,
                icon: 'success'
            });
        },

        // 🎯 新增：创建模板PDF对象的辅助方法
        createTemplatePDFObject(selectionData, fileName) {
            return {
                file_id: selectionData.formData.pdf_info.file_id,
                file_name: fileName,
                file_size: selectionData.template.file_info?.file_size || 0,
                cloud_path: selectionData.formData.pdf_info.cloud_path || '',
                url: selectionData.template.file_info?.url || '',
                source: 'template', // 标记为模板来源
                template_id: selectionData.template._id,
                template_source: selectionData.template
            };
        },

        // 🎯 修改：清除模板选择 - 只清除模板文件，保留上传文件
        clearTemplateSelection() {
            console.log('清除模板选择')
            this.selectedTemplate = null
            // 🎯 只移除模板来源的文件，保留本地上传的文件
            this.formData.pdf_files = this.formData.pdf_files.filter(file => file.source !== 'template')

            if (this.formData.pdf_files.length > 0) {
                uni.showToast({
                    title: `已清除模板文件，保留${this.formData.pdf_files.length}个上传文件`,
                    icon: 'success'
                });
            } else {
                uni.showToast({
                    title: '已清除模板选择',
                    icon: 'success'
                });
            }
        },

        // 新增方法：格式化文件大小
        formatFileSize(bytes) {
            if (!bytes || bytes === 0) return '0 B'
            const k = 1024
            const sizes = ['B', 'KB', 'MB', 'GB']
            const i = Math.floor(Math.log(bytes) / Math.log(k))
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
        },

        // 新增方法：获取短文件名（用于预览显示）
        getShortFileName(fileName) {
            if (!fileName) return '未知文件'
            if (fileName.length <= 12) return fileName

            const lastDot = fileName.lastIndexOf('.')
            if (lastDot > -1) {
                const name = fileName.substring(0, lastDot)
                const ext = fileName.substring(lastDot)
                if (name.length > 8) {
                    return name.substring(0, 8) + '...' + ext
                }
            }
            return fileName.substring(0, 12) + '...'
        },

        // 新增方法：预览PDF文件 - 支持多文件
        previewPDF(pdfFile, index) {
            const pdfUrl = pdfFile?.url
            if (pdfUrl) {
                console.log('预览PDF:', pdfFile.file_name, pdfUrl)
                // 可以在这里添加PDF预览逻辑
                uni.showToast({
                    title: 'PDF预览功能开发中',
                    icon: 'none'
                })
            }
        },

        // 🎯 修改：移除单个PDF文件 - 显示文件来源信息
        removePDFFile(index) {
            const file = this.formData.pdf_files[index];
            const sourceText = file.source === 'template' ? '模板文件' : '上传文件';

            uni.showModal({
                title: '确认删除',
                content: `是否删除该${sourceText}：${file.file_name}？`,
                success: (res) => {
                    if (res.confirm) {
                        this.formData.pdf_files.splice(index, 1)
                        uni.showToast({
                            title: `${sourceText}删除成功`,
                            icon: 'success'
                        })
                    }
                }
            })
        },

        // 🎯 新增：清除所有上传文件，保留模板文件
        clearUploadedFiles() {
            const uploadedCount = this.formData.pdf_files.filter(file => file.source === 'upload').length;
            if (uploadedCount === 0) {
                uni.showToast({
                    title: '没有上传文件需要清除',
                    icon: 'none'
                });
                return;
            }

            uni.showModal({
                title: '确认清除',
                content: `是否清除所有${uploadedCount}个上传文件？模板文件将保留。`,
                success: (res) => {
                    if (res.confirm) {
                        this.formData.pdf_files = this.formData.pdf_files.filter(file => file.source !== 'upload');
                        uni.showToast({
                            title: `已清除${uploadedCount}个上传文件`,
                            icon: 'success'
                        });
                    }
                }
            });
        },

        // 🎯 修改：清空PDF文件 - 提供选择性清空选项
        clearAllPDFFiles() {
            if (!this.formData.pdf_files || this.formData.pdf_files.length === 0) {
                uni.showToast({
                    title: '没有PDF文件',
                    icon: 'none'
                })
                return
            }

            const uploadCount = this.formData.pdf_files.filter(file => file.source === 'upload').length;
            const templateCount = this.formData.pdf_files.filter(file => file.source === 'template').length;
            const totalCount = this.formData.pdf_files.length;

            // 如果只有一种类型的文件，直接清空
            if (uploadCount === 0) {
                this.clearTemplateFiles(templateCount);
            } else if (templateCount === 0) {
                this.clearUploadFiles(uploadCount);
            } else {
                // 有两种类型的文件，提供选择
                this.showClearOptionsModal(uploadCount, templateCount, totalCount);
            }
        },

        // 🎯 新增：显示清空选项模态框
        showClearOptionsModal(uploadCount, templateCount, totalCount) {
            uni.showActionSheet({
                itemList: [
                    `清空所有文件 (${totalCount}个)`,
                    `仅清空上传文件 (${uploadCount}个)`,
                    `仅清空模板文件 (${templateCount}个)`
                ],
                success: (res) => {
                    switch (res.tapIndex) {
                        case 0:
                            this.clearAllFiles(totalCount);
                            break;
                        case 1:
                            this.clearUploadFiles(uploadCount);
                            break;
                        case 2:
                            this.clearTemplateFiles(templateCount);
                            break;
                    }
                }
            });
        },

        // 🎯 新增：清空所有文件
        clearAllFiles(totalCount) {
            uni.showModal({
                title: '确认清空',
                content: `是否清空所有 ${totalCount} 个PDF文件？`,
                success: (res) => {
                    if (res.confirm) {
                        this.formData.pdf_files = [];
                        this.selectedTemplate = null;
                        uni.showToast({
                            title: '已清空所有文件',
                            icon: 'success'
                        });
                    }
                }
            });
        },

        // 🎯 新增：清空上传文件
        clearUploadFiles(uploadCount) {
            uni.showModal({
                title: '确认清空',
                content: `是否清空所有 ${uploadCount} 个上传文件？模板文件将保留。`,
                success: (res) => {
                    if (res.confirm) {
                        this.formData.pdf_files = this.formData.pdf_files.filter(file => file.source !== 'upload');
                        uni.showToast({
                            title: `已清空${uploadCount}个上传文件`,
                            icon: 'success'
                        });
                    }
                }
            });
        },

        // 🎯 新增：清空模板文件
        clearTemplateFiles(templateCount) {
            uni.showModal({
                title: '确认清空',
                content: `是否清空所有 ${templateCount} 个模板文件？上传文件将保留。`,
                success: (res) => {
                    if (res.confirm) {
                        this.formData.pdf_files = this.formData.pdf_files.filter(file => file.source !== 'template');
                        this.selectedTemplate = null;
                        uni.showToast({
                            title: `已清空${templateCount}个模板文件`,
                            icon: 'success'
                        });
                    }
                }
            });
        },

        // 发送家长信
        async sendLetter() {
            if (this.loading) return

            console.log('开始发送家长信，当前pdf_files:', this.formData.pdf_files)

            if (!await this.validateForm()) {
                console.log('表单验证失败，无法发送')
                return
            }

            // 验证PDF文件 - 支持多文件
            if (!this.formData.pdf_files || !Array.isArray(this.formData.pdf_files) || this.formData.pdf_files.length === 0) {
                console.log('PDF文件列表为空，无法发送')
                uni.showToast({
                    title: '请添加PDF文件',
                    icon: 'error'
                })
                return
            }

            // 验证级别
            // if (!this.formData.level) {
            //     console.log('教材级别为空，无法发送')
            //     uni.showToast({
            //         title: '请选择教材级别',
            //         icon: 'error'
            //     })
            //     return
            // }

            // 确保teacher_id不为空
            if (!this.formData.teacher_id) {
                console.log('teacher_id为空，尝试获取当前用户ID')
                const userInfo = this.$store.state.user.userInfo || uni.getStorageSync('uni-id-pages-userInfo')
                if (userInfo && userInfo._id) {
                    this.formData.teacher_id = userInfo._id

                    // 设置teacher_name，为超级管理员添加特殊标识
                    let displayName = userInfo.nickname || userInfo.username || '管理员'

                    // 检查是否是超级管理员
                    if (userInfo.role && (userInfo.role.includes('admin') || userInfo.role.includes('administrator'))) {
                        // 为超级管理员添加标识
                        this.formData.teacher_name = `[管理员] ${displayName}`
                    } else {
                        this.formData.teacher_name = displayName
                    }

                    console.log('已设置teacher_id:', this.formData.teacher_id)
                    console.log('已设置teacher_name:', this.formData.teacher_name)
                } else {
                    console.error('无法获取用户ID')
                    uni.showToast({
                        title: '请先登录后再操作',
                        icon: 'none'
                    })
                    return
                }
            }

            // 确保teacher_name不为空
            if (!this.formData.teacher_name) {
                console.log('teacher_name为空，设置默认值')
                this.formData.teacher_name = '[管理员] 系统管理员'
            }

            this.loading = true

            try {
                // 准备家长信数据
                const letterData = {
                    ...this.formData,
                    status: 'sent',
                    // 使用$env: "now"让uniCloud自动处理时间戳
                    send_time: {
                        $env: "now"
                    }
                    // 移除create_time和update_time，让uniCloud自动处理
                    // create_time: new Date(),
                    // update_time: new Date()
                }

                // 输出完整的提交数据，用于调试
                console.log('准备提交的家长信数据:', JSON.stringify(letterData, null, 2))

                // 保存家长信记录
                const letterResult = await db.collection('parent-letter').add(letterData)
                console.log('家长信保存成功:', letterResult)

                // 如果是本地上传模式，需要保存到模板库（上传时已经自动保存了）
                // 如果是模板选择模式，需要更新模板使用统计
                if (this.formData.content.creation_method === 'template' && this.selectedTemplate) {
                    await this.updateTemplateUsageStats(this.selectedTemplate._id)
                }

                uni.showToast({
                    title: '发送成功',
                    icon: 'success'
                })

                setTimeout(() => {
                    uni.navigateBack({
                        success: () => {
                            uni.$emit('refreshData')
                        }
                    })
                }, 1500)
            } catch (error) {
                console.error('发送失败', error)
                uni.showToast({
                    title: '发送失败',
                    icon: 'error'
                })
            } finally {
                this.loading = false
            }
        },

        // 新增方法：更新模板使用统计
        async updateTemplateUsageStats(templateId) {
            try {
                console.log('更新模板使用统计:', templateId)

                await db.collection('parent-letter-template')
                    .doc(templateId)
                    .update({
                        'usage_statistics.usage_count': db.command.inc(1),
                        'usage_statistics.last_used': {
                            $env: "now"
                        }
                        // 移除update_time，让uniCloud自动处理
                        // update_time: new Date()
                    })

                console.log('模板使用统计更新成功')
            } catch (error) {
                console.error('更新模板使用统计失败:', error)
                // 不影响主流程，只记录错误
            }
        },

        // 保存草稿
        async saveDraft() {
            if (this.loading) return

            // 基本验证（草稿不需要完整验证）
            if (!this.formData.title.trim()) {
                uni.showToast({
                    title: '请输入家长信标题',
                    icon: 'error'
                })
                return
            }

            if (!this.formData.class_ids || this.formData.class_ids.length === 0) {
                uni.showToast({
                    title: '请选择班级',
                    icon: 'error'
                })
                return
            }

            this.loading = true

            try {
                const result = await db.collection('parent-letter').add({
                    ...this.formData,
                    status: 'draft'
                    // 移除create_time和update_time，让uniCloud自动处理
                    // create_time: new Date(),
                    // update_time: new Date()
                })

                uni.showToast({
                    title: '草稿已保存',
                    icon: 'success'
                })

                setTimeout(() => {
                    uni.navigateBack({
                        success: () => {
                            uni.$emit('refreshData')
                        }
                    })
                }, 1500)
            } catch (error) {
                console.error('保存草稿失败', error)
                uni.showToast({
                    title: '保存失败',
                    icon: 'error'
                })
            } finally {
                this.loading = false
            }
        },

        // 新增方法：优化表单数据结构
        optimizeFormData() {
            // 确保PDF信息结构完整
            if (this.formData.pdf_info) {
                // 标准化PDF信息结构
                this.formData.pdf_info = {
                    file_id: this.formData.pdf_info.file_id || '',
                    file_name: this.formData.pdf_info.file_name || '',
                    file_size: this.formData.pdf_info.file_size || 0,
                    cloud_path: this.formData.pdf_info.cloud_path || '',
                    template_source: this.formData.pdf_info.template_source || null
                }
            }

            // 确保pdf_files是数组
            if (!this.formData.pdf_files) {
                this.formData.pdf_files = []
            }

            // 确保class_ids是数组
            if (!this.formData.class_ids) {
                this.formData.class_ids = []
            }

            // 确保级别和标签数据格式正确
            this.formData.level = this.formData.level || ''
            this.formData.tags = Array.isArray(this.formData.tags) ? this.formData.tags : []

            // 确保teacher_id和teacher_name不为空
            if (!this.formData.teacher_id) {
                const userInfo = this.$store.state.user.userInfo || uni.getStorageSync('uni-id-pages-userInfo')
                if (userInfo && userInfo._id) {
                    this.formData.teacher_id = userInfo._id
                }
            }

            if (!this.formData.teacher_name) {
                const userInfo = this.$store.state.user.userInfo || uni.getStorageSync('uni-id-pages-userInfo')
                if (userInfo) {
                    let displayName = userInfo.nickname || userInfo.username || '管理员'

                    // 检查是否是超级管理员
                    if (userInfo.role && (userInfo.role.includes('admin') || userInfo.role.includes('administrator'))) {
                        // 为超级管理员添加标识
                        this.formData.teacher_name = `[管理员] ${displayName}`
                    } else {
                        this.formData.teacher_name = displayName
                    }
                } else {
                    this.formData.teacher_name = '[管理员] 系统管理员'
                }
            }

            // 确保接收者信息完整
            if (!this.formData.recipients.student_count) {
                this.formData.recipients.student_count = this.formData.recipients.student_ids ? this.formData.recipients.student_ids.length : 0
            }

            return this.formData
        },

        // 新增方法：验证创建流程完整性
        validateCreationFlow() {
            const errors = []

            // 验证基本信息
            if (!this.formData.title.trim()) {
                errors.push('请输入家长信标题')
            }

            // if (!this.formData.type) {
            //     errors.push('请选择家长信类型')
            // }

            // if (!this.formData.level) {
            //     errors.push('请选择教材级别')
            // }

            if (!this.formData.class_ids || this.formData.class_ids.length === 0) {
                errors.push('请选择发布班级')
            }

            // 验证创建方式和PDF信息
            if (!this.formData.content.creation_method) {
                errors.push('请选择创建方式')
            } else {
                if (this.formData.content.creation_method === 'upload') {
                    if (!this.formData.pdf_files || this.formData.pdf_files.length === 0) {
                        errors.push('请上传PDF文件')
                    }
                } else if (this.formData.content.creation_method === 'template') {
                    if (!this.selectedTemplate || this.formData.pdf_files.length === 0) {
                        errors.push('请选择PDF模板')
                    }
                }
            }

            // 验证接收者信息
            if (!this.formData.recipients.student_ids || this.formData.recipients.student_ids.length === 0) {
                errors.push('请选择接收学生')
            }

            return {
                valid: errors.length === 0,
                errors: errors
            }
        },

        // 新增方法：处理界面切换逻辑
        handleInterfaceSwitch(method) {
            console.log('处理界面切换:', method)

            // 保存当前状态
            const currentPdfFiles = this.formData.pdf_files // 改为 pdf_files
            const currentTemplate = this.selectedTemplate

            // 切换创建方式
            this.formData.content.creation_method = method

            // 根据切换方式处理数据
            if (method === 'upload') {
                // 切换到上传模式，清除模板选择
                this.selectedTemplate = null
                if (currentTemplate) {
                    // 如果之前选择了模板，询问是否保留PDF信息
                    uni.showModal({
                        title: '切换创建方式',
                        content: '切换到本地上传模式将清除已选择的模板，是否继续？',
                        success: (res) => {
                            if (res.confirm) {
                                this.formData.pdf_files = [] // 清空PDF文件列表
                            } else {
                                // 用户取消，恢复之前的选择
                                this.formData.content.creation_method = 'template'
                                this.selectedTemplate = currentTemplate
                            }
                        }
                    })
                }
            } else if (method === 'template') {
                // 切换到模板模式，清除上传的文件
                if (currentPdfFiles && currentPdfFiles.length > 0) { // 改为 pdf_files
                    // 如果之前上传了文件，询问是否保留
                    uni.showModal({
                        title: '切换创建方式',
                        content: '切换到模板选择模式将清除已上传的文件，是否继续？',
                        success: (res) => {
                            if (res.confirm) {
                                this.formData.pdf_files = [] // 清空PDF文件列表
                                // 重置PDF上传组件
                                if (this.$refs.pdfUploader) {
                                    this.$refs.pdfUploader.reset()
                                }
                            } else {
                                // 用户取消，恢复之前的选择
                                this.formData.content.creation_method = 'upload'
                            }
                        }
                    })
                }
            }
        }
    }
}
</script>

<style lang="scss">
.fix-top-window {
    padding-top: 0px;
}

.uni-container {
    padding: 15px;
}

.uni-header {
    padding: 0 15px;
    display: flex;
    height: 55px;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid #e5e5e5;

    .uni-group {
        display: flex;
        align-items: center;

        .uni-title {
            font-size: 18px;
            font-weight: bold;
        }
    }
}

.uni-content {
    background: #fff;
    border-radius: 5px;
    padding: 20px;
}

// 级别标签样式
.level-tags-container {
    border: 1px solid #ddd;
    border-radius: 6px;
    background-color: #fff;
    overflow: hidden;
    padding: 15px;

    .level-tags-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12px;

        .level-title {
            font-size: 13px;
            color: #666;
            font-weight: 500;
        }
    }

    .level-tags-list {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
        margin-bottom: 15px;

        .level-tag {
            padding: 6px 12px;
            border: 1px solid #ddd;
            border-radius: 16px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.2s;

            &:hover {
                border-color: #007AFF;
                color: #007AFF;
            }

            &.selected {
                background-color: #007AFF;
                color: #fff;
                border-color: #007AFF;
            }
        }
    }

    .selected-level {
        margin-top: 15px;
        border-top: 1px solid #eee;
        padding-top: 15px;

        .selected-level-label {
            display: block;
            margin-bottom: 10px;
            font-size: 14px;
            color: #666;
        }

        .selected-level-tag {
            display: inline-flex;
            align-items: center;
            padding: 6px 10px;
            background-color: #007AFF;
            color: #fff;
            border-radius: 16px;
            font-size: 12px;

            .remove-btn {
                font-size: 14px;
                font-weight: bold;
                cursor: pointer;
                width: 16px;
                height: 16px;
                line-height: 16px;
                text-align: center;
                border-radius: 50%;
                margin-left: 4px;
                transition: background-color 0.2s;

                &:hover {
                    background-color: rgba(255, 255, 255, 0.2);
                }
            }
        }
    }
}

.student-selection {
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid #e5e5e5;
    border-radius: 5px;
    padding: 10px;
}

.template-variables {
    margin-top: 20px;

    .variable-item {
        margin-bottom: 15px;
    }
}

.editor {
    min-height: 300px;
    border: 1px solid #ddd;
    border-radius: 5px;
    padding: 10px;
}

.uni-button-group {
    margin-top: 50px;
    display: flex;
    justify-content: center;
}

::v-deep .uni-forms-item__label {
    width: 90px !important;
}

// 班级和学生选择样式
.class-selector,
.student-selector {
    display: flex;
    flex-wrap: wrap;
    padding: 5px 0;
}

.class-item,
.student-item {
    display: flex;
    align-items: center;
    margin-right: 15px;
    margin-bottom: 10px;
    padding: 5px 10px;
    border: 1px solid #eee;
    border-radius: 5px;
}

.class-name,
.student-name {
    margin-left: 5px;
}

.empty-tip {
    color: #999;
    font-size: 14px;
    padding: 10px 0;
}

.tip-message {
    width: 100%;
    color: #666;
    font-size: 14px;
    margin-bottom: 10px;
    padding: 5px;
    background-color: #f8f8f8;
    border-radius: 4px;
}

// PDF上传区域 - 与作业页面多媒体样式完全一致
.pdf-upload-section {
    border: 1px solid #eee;
    border-radius: 8px;
    overflow: hidden;
}

.upload-buttons {
    display: flex;
    gap: 10px;
    padding: 15px;
}

.upload-btn {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 8px 16px;
    border: 1px solid #ddd;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s;
    background-color: #fff;
}

.upload-btn:hover {
    border-color: #409eff;
    background-color: #f0f8ff;
}

.btn-text {
    font-size: 14px;
    color: #333;
}

// PDF预览区域 - 与作业页面完全一致
.pdf-preview-area {
    padding: 15px;
    border-top: 1px solid #eee;
}

.preview-grid {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-bottom: 15px;
}

.preview-item {
    position: relative;
}

.preview-content {
    position: relative;
    width: 80px;
    height: 80px;
    border: 1px solid #eee;
    border-radius: 6px;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    box-sizing: border-box;
    cursor: pointer;
    transition: all 0.3s;
    background-color: #fafafa;
    padding: 8px;
}

.preview-content:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.file-icon {
    margin-bottom: 4px;
}

.file-name {
    font-size: 10px;
    color: #666;
    text-align: center;
    line-height: 1.2;
    word-break: break-all;
}

// 文件类型标识
.file-type-badge {
    position: absolute;
    top: 2px;
    left: 2px;
    background-color: rgba(64, 158, 255, 0.8);
    color: #fff;
    font-size: 8px;
    padding: 1px 4px;
    border-radius: 3px;
    text-transform: uppercase;
    font-weight: 500;
}

// 模板标识
.template-badge {
    position: absolute;
    bottom: 2px;
    right: 2px;
    background-color: rgba(103, 194, 58, 0.8);
    color: #fff;
    font-size: 8px;
    padding: 1px 4px;
    border-radius: 3px;
    font-weight: 500;
}

// 秒传标识
.second-upload-badge {
    position: absolute;
    bottom: 2px;
    left: 2px;
    background-color: rgba(230, 162, 60, 0.8);
    color: #fff;
    font-size: 8px;
    padding: 1px 4px;
    border-radius: 3px;
    font-weight: 500;
}

.badge-text {
    color: #fff;
    font-size: 8px;
}

// 删除按钮 - 与作业页面完全一致
.delete-btn {
    position: absolute;
    top: -6px;
    right: -6px;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background-color: #f56c6c;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: all 0.3s;
}

.delete-btn:hover {
    background-color: #f78989;
    transform: scale(1.1);
}

// 文件信息显示 - 与作业页面保持一致
.file-count {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 12px;
    color: #666;
}

.count-text {
    font-weight: 500;
}

.file-info-detail {
    color: #999;
    font-size: 11px;
    margin-left: 8px;
}

// 支持格式提示 - 与作业页面保持一致
.format-tips {
    padding: 12px 20px;
    background-color: #f8f9fa;
    border-top: 1px solid #eee;
}

.tips-text {
    font-size: 12px;
    color: #909399;
    line-height: 1.4;
}

// 新增样式：模板选择器包装器
.template-selector-wrapper {
    .template-placeholder {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 40px 20px;
        border: 2px dashed #ddd;
        border-radius: 8px;
        cursor: pointer;
        transition: all 0.3s;

        &:hover {
            border-color: #007AFF;
            background-color: #f8f9ff;
        }

        .placeholder-text {
            font-size: 16px;
            color: #333;
            margin-top: 10px;
            font-weight: 500;
        }

        .placeholder-hint {
            font-size: 12px;
            color: #999;
            margin-top: 5px;
        }
    }

    .selected-template-info {
        .template-file-info {
            display: flex;
            align-items: flex-start;
            padding: 15px;
            border: 1px solid #007AFF;
            border-radius: 8px;
            background-color: #f8f9ff;

            .file-details {
                flex: 1;
                margin-left: 12px;

                .file-name {
                    font-size: 16px;
                    font-weight: 500;
                    color: #333;
                    margin-bottom: 8px;
                    word-break: break-all;
                }

                .file-meta {
                    font-size: 12px;
                    color: #666;
                    margin-bottom: 8px;
                }

                .template-tags {
                    display: flex;
                    flex-wrap: wrap;
                    gap: 5px;

                    .template-tag {
                        padding: 2px 8px;
                        background-color: #e3f2fd;
                        color: #1976d2;
                        border-radius: 12px;
                        font-size: 11px;
                    }
                }
            }

            .template-actions {
                display: flex;
                flex-direction: column;
                gap: 8px;

                .mini-btn {
                    font-size: 12px;
                    padding: 4px 8px;
                    height: auto;
                    line-height: 1.4;
                    min-width: 60px;
                }
            }
        }
    }
}

// 新增样式：上传和模板区域
.upload-section,
.template-section {
    margin-top: 10px;
}

// 响应式设计
@media screen and (max-width: 768px) {
    .uni-container {
        padding: 10px;
    }

    .uni-content {
        padding: 15px;
    }

    .uni-button-group {
        flex-direction: column;

        .uni-button {
            width: 100%;
            margin-bottom: 10px;
        }
    }

    .creation-method-selector {
        .creation-method-item {
            padding: 12px;

            .method-name {
                font-size: 14px;
            }

            .method-desc {
                font-size: 11px;
            }
        }
    }

    .template-selector-wrapper {
        .template-placeholder {
            padding: 30px 15px;

            .placeholder-text {
                font-size: 14px;
            }
        }

        .selected-template-info {
            .template-file-info {
                padding: 12px;

                .file-details {
                    .file-name {
                        font-size: 14px;
                    }
                }

                .template-actions {
                    .mini-btn {
                        font-size: 11px;
                        padding: 3px 6px;
                        min-width: 50px;
                    }
                }
            }
        }
    }
}
</style>