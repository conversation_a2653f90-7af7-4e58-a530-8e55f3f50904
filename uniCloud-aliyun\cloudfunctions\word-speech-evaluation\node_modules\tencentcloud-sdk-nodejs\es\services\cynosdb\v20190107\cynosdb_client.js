import { AbstractClient } from "../../../common/abstract_client";
export class Client extends AbstractClient {
    constructor(clientConfig) {
        super("cynosdb.tencentcloudapi.com", "2019-01-07", clientConfig);
    }
    async ResumeServerless(req, cb) {
        return this.request("ResumeServerless", req, cb);
    }
    async UpgradeProxyVersion(req, cb) {
        return this.request("UpgradeProxyVersion", req, cb);
    }
    async DescribeBackupConfig(req, cb) {
        return this.request("DescribeBackupConfig", req, cb);
    }
    async ReplayInstanceAuditLog(req, cb) {
        return this.request("ReplayInstanceAuditLog", req, cb);
    }
    async ModifyBinlogConfig(req, cb) {
        return this.request("ModifyBinlogConfig", req, cb);
    }
    async CreateProxyEndPoint(req, cb) {
        return this.request("CreateProxyEndPoint", req, cb);
    }
    async ModifyResourcePackageClusters(req, cb) {
        return this.request("ModifyResourcePackageClusters", req, cb);
    }
    async CreateAuditRuleTemplate(req, cb) {
        return this.request("CreateAuditRuleTemplate", req, cb);
    }
    async GrantAccountPrivileges(req, cb) {
        return this.request("GrantAccountPrivileges", req, cb);
    }
    async DescribeClusterParamLogs(req, cb) {
        return this.request("DescribeClusterParamLogs", req, cb);
    }
    async ModifyMaintainPeriodConfig(req, cb) {
        return this.request("ModifyMaintainPeriodConfig", req, cb);
    }
    async CopyClusterPasswordComplexity(req, cb) {
        return this.request("CopyClusterPasswordComplexity", req, cb);
    }
    async ModifyDBInstanceSecurityGroups(req, cb) {
        return this.request("ModifyDBInstanceSecurityGroups", req, cb);
    }
    async CloseAuditService(req, cb) {
        return this.request("CloseAuditService", req, cb);
    }
    async DescribeSupportProxyVersion(req, cb) {
        return this.request("DescribeSupportProxyVersion", req, cb);
    }
    async CreateAuditLogFile(req, cb) {
        return this.request("CreateAuditLogFile", req, cb);
    }
    async DescribeSlaveZones(req, cb) {
        return this.request("DescribeSlaveZones", req, cb);
    }
    async CloseProxyEndPoint(req, cb) {
        return this.request("CloseProxyEndPoint", req, cb);
    }
    async CloseProxy(req, cb) {
        return this.request("CloseProxy", req, cb);
    }
    async DescribeProxySpecs(req, cb) {
        return this.request("DescribeProxySpecs", req, cb);
    }
    async InquirePriceModify(req, cb) {
        return this.request("InquirePriceModify", req, cb);
    }
    async SwitchProxyVpc(req, cb) {
        return this.request("SwitchProxyVpc", req, cb);
    }
    async DescribeClusterDetail(req, cb) {
        return this.request("DescribeClusterDetail", req, cb);
    }
    async DescribeTasks(req, cb) {
        return this.request("DescribeTasks", req, cb);
    }
    async DescribeInstanceSpecs(req, cb) {
        return this.request("DescribeInstanceSpecs", req, cb);
    }
    async DescribeBinlogDownloadUrl(req, cb) {
        return this.request("DescribeBinlogDownloadUrl", req, cb);
    }
    async DescribeResourcesByDealName(req, cb) {
        return this.request("DescribeResourcesByDealName", req, cb);
    }
    async ModifyClusterStorage(req, cb) {
        return this.request("ModifyClusterStorage", req, cb);
    }
    async ActivateInstance(req, cb) {
        return this.request("ActivateInstance", req, cb);
    }
    async DescribeProxyNodes(req, cb) {
        return this.request("DescribeProxyNodes", req, cb);
    }
    async RenewClusters(req, cb) {
        return this.request("RenewClusters", req, cb);
    }
    async DisassociateSecurityGroups(req, cb) {
        return this.request("DisassociateSecurityGroups", req, cb);
    }
    async DescribeClusterTransparentEncryptInfo(req, cb) {
        return this.request("DescribeClusterTransparentEncryptInfo", req, cb);
    }
    async ExportInstanceSlowQueries(req, cb) {
        return this.request("ExportInstanceSlowQueries", req, cb);
    }
    async DeleteCLSDelivery(req, cb) {
        return this.request("DeleteCLSDelivery", req, cb);
    }
    async DescribeChangedParamsAfterUpgrade(req, cb) {
        return this.request("DescribeChangedParamsAfterUpgrade", req, cb);
    }
    async DescribeAccountPrivileges(req, cb) {
        return this.request("DescribeAccountPrivileges", req, cb);
    }
    async CloseClusterPasswordComplexity(req, cb) {
        return this.request("CloseClusterPasswordComplexity", req, cb);
    }
    async RollBackCluster(req, cb) {
        return this.request("RollBackCluster", req, cb);
    }
    async DescribeBinlogSaveDays(req, cb) {
        return this.request("DescribeBinlogSaveDays", req, cb);
    }
    async SearchClusterTables(req, cb) {
        return this.request("SearchClusterTables", req, cb);
    }
    async DescribeClusterInstanceGrps(req, cb) {
        return this.request("DescribeClusterInstanceGrps", req, cb);
    }
    async DescribeIsolatedInstances(req, cb) {
        return this.request("DescribeIsolatedInstances", req, cb);
    }
    async DescribeInstanceErrorLogs(req, cb) {
        return this.request("DescribeInstanceErrorLogs", req, cb);
    }
    async ModifyClusterName(req, cb) {
        return this.request("ModifyClusterName", req, cb);
    }
    async ModifyProxyRwSplit(req, cb) {
        return this.request("ModifyProxyRwSplit", req, cb);
    }
    async ModifyVipVport(req, cb) {
        return this.request("ModifyVipVport", req, cb);
    }
    async DeleteAuditRuleTemplates(req, cb) {
        return this.request("DeleteAuditRuleTemplates", req, cb);
    }
    async ModifyProxyDesc(req, cb) {
        return this.request("ModifyProxyDesc", req, cb);
    }
    async CreateParamTemplate(req, cb) {
        return this.request("CreateParamTemplate", req, cb);
    }
    async ModifyClusterSlaveZone(req, cb) {
        return this.request("ModifyClusterSlaveZone", req, cb);
    }
    async ModifyAccountParams(req, cb) {
        return this.request("ModifyAccountParams", req, cb);
    }
    async ModifyClusterPasswordComplexity(req, cb) {
        return this.request("ModifyClusterPasswordComplexity", req, cb);
    }
    async CreateBackup(req, cb) {
        return this.request("CreateBackup", req, cb);
    }
    async OfflineInstance(req, cb) {
        return this.request("OfflineInstance", req, cb);
    }
    async IsolateCluster(req, cb) {
        return this.request("IsolateCluster", req, cb);
    }
    async DescribeInstancesWithinSameCluster(req, cb) {
        return this.request("DescribeInstancesWithinSameCluster", req, cb);
    }
    async RestartInstance(req, cb) {
        return this.request("RestartInstance", req, cb);
    }
    async UpgradeProxy(req, cb) {
        return this.request("UpgradeProxy", req, cb);
    }
    async DescribeClusters(req, cb) {
        return this.request("DescribeClusters", req, cb);
    }
    async CloseWan(req, cb) {
        return this.request("CloseWan", req, cb);
    }
    async DescribeProjectSecurityGroups(req, cb) {
        return this.request("DescribeProjectSecurityGroups", req, cb);
    }
    async ModifyClusterDatabase(req, cb) {
        return this.request("ModifyClusterDatabase", req, cb);
    }
    async ModifyAuditRuleTemplates(req, cb) {
        return this.request("ModifyAuditRuleTemplates", req, cb);
    }
    async DescribeBackupDownloadUserRestriction(req, cb) {
        return this.request("DescribeBackupDownloadUserRestriction", req, cb);
    }
    async ModifyParamTemplate(req, cb) {
        return this.request("ModifyParamTemplate", req, cb);
    }
    async ModifyResourcePackagesDeductionPriority(req, cb) {
        return this.request("ModifyResourcePackagesDeductionPriority", req, cb);
    }
    async DescribeInstanceParams(req, cb) {
        return this.request("DescribeInstanceParams", req, cb);
    }
    async DescribeInstanceSlowQueries(req, cb) {
        return this.request("DescribeInstanceSlowQueries", req, cb);
    }
    async DescribeClusterDatabases(req, cb) {
        return this.request("DescribeClusterDatabases", req, cb);
    }
    async ModifyClusterParam(req, cb) {
        return this.request("ModifyClusterParam", req, cb);
    }
    async CreateClusters(req, cb) {
        return this.request("CreateClusters", req, cb);
    }
    async ModifyBackupDownloadUserRestriction(req, cb) {
        return this.request("ModifyBackupDownloadUserRestriction", req, cb);
    }
    async CreateAccounts(req, cb) {
        return this.request("CreateAccounts", req, cb);
    }
    async RollbackToNewCluster(req, cb) {
        return this.request("RollbackToNewCluster", req, cb);
    }
    async CreateResourcePackage(req, cb) {
        return this.request("CreateResourcePackage", req, cb);
    }
    async DescribeAuditLogFiles(req, cb) {
        return this.request("DescribeAuditLogFiles", req, cb);
    }
    async DeleteClusterDatabase(req, cb) {
        return this.request("DeleteClusterDatabase", req, cb);
    }
    async SwitchClusterZone(req, cb) {
        return this.request("SwitchClusterZone", req, cb);
    }
    async SearchClusterDatabases(req, cb) {
        return this.request("SearchClusterDatabases", req, cb);
    }
    async AddInstances(req, cb) {
        return this.request("AddInstances", req, cb);
    }
    async ModifyInstanceUpgradeLimitDays(req, cb) {
        return this.request("ModifyInstanceUpgradeLimitDays", req, cb);
    }
    async DeleteAccounts(req, cb) {
        return this.request("DeleteAccounts", req, cb);
    }
    async RevokeAccountPrivileges(req, cb) {
        return this.request("RevokeAccountPrivileges", req, cb);
    }
    async OpenClusterReadOnlyInstanceGroupAccess(req, cb) {
        return this.request("OpenClusterReadOnlyInstanceGroupAccess", req, cb);
    }
    async OpenClusterTransparentEncrypt(req, cb) {
        return this.request("OpenClusterTransparentEncrypt", req, cb);
    }
    async DescribeParamTemplates(req, cb) {
        return this.request("DescribeParamTemplates", req, cb);
    }
    async DeleteBackup(req, cb) {
        return this.request("DeleteBackup", req, cb);
    }
    async DescribeServerlessStrategy(req, cb) {
        return this.request("DescribeServerlessStrategy", req, cb);
    }
    async PauseServerless(req, cb) {
        return this.request("PauseServerless", req, cb);
    }
    async ModifyAccountHost(req, cb) {
        return this.request("ModifyAccountHost", req, cb);
    }
    async ResetAccountPassword(req, cb) {
        return this.request("ResetAccountPassword", req, cb);
    }
    async DescribeInstanceDetail(req, cb) {
        return this.request("DescribeInstanceDetail", req, cb);
    }
    async DescribeAuditInstanceList(req, cb) {
        return this.request("DescribeAuditInstanceList", req, cb);
    }
    async DescribeRollbackTimeRange(req, cb) {
        return this.request("DescribeRollbackTimeRange", req, cb);
    }
    async DescribeFlow(req, cb) {
        return this.request("DescribeFlow", req, cb);
    }
    async ModifyInstanceParam(req, cb) {
        return this.request("ModifyInstanceParam", req, cb);
    }
    async DescribeInstanceCLSLogDelivery(req, cb) {
        return this.request("DescribeInstanceCLSLogDelivery", req, cb);
    }
    async BindClusterResourcePackages(req, cb) {
        return this.request("BindClusterResourcePackages", req, cb);
    }
    async DescribeServerlessInstanceSpecs(req, cb) {
        return this.request("DescribeServerlessInstanceSpecs", req, cb);
    }
    async DescribeAuditLogs(req, cb) {
        return this.request("DescribeAuditLogs", req, cb);
    }
    async UnbindClusterResourcePackages(req, cb) {
        return this.request("UnbindClusterResourcePackages", req, cb);
    }
    async DescribeProxies(req, cb) {
        return this.request("DescribeProxies", req, cb);
    }
    async OpenWan(req, cb) {
        return this.request("OpenWan", req, cb);
    }
    async InquirePriceCreate(req, cb) {
        return this.request("InquirePriceCreate", req, cb);
    }
    async AssociateSecurityGroups(req, cb) {
        return this.request("AssociateSecurityGroups", req, cb);
    }
    async DescribeClusterReadOnly(req, cb) {
        return this.request("DescribeClusterReadOnly", req, cb);
    }
    async DescribeResourcePackageSaleSpec(req, cb) {
        return this.request("DescribeResourcePackageSaleSpec", req, cb);
    }
    async ModifyAccountPrivileges(req, cb) {
        return this.request("ModifyAccountPrivileges", req, cb);
    }
    async DescribeAuditRuleWithInstanceIds(req, cb) {
        return this.request("DescribeAuditRuleWithInstanceIds", req, cb);
    }
    async DescribeSSLStatus(req, cb) {
        return this.request("DescribeSSLStatus", req, cb);
    }
    async ExportInstanceErrorLogs(req, cb) {
        return this.request("ExportInstanceErrorLogs", req, cb);
    }
    async AddClusterSlaveZone(req, cb) {
        return this.request("AddClusterSlaveZone", req, cb);
    }
    async ModifyBackupName(req, cb) {
        return this.request("ModifyBackupName", req, cb);
    }
    async DescribeAccounts(req, cb) {
        return this.request("DescribeAccounts", req, cb);
    }
    async SetRenewFlag(req, cb) {
        return this.request("SetRenewFlag", req, cb);
    }
    async InquirePriceRenew(req, cb) {
        return this.request("InquirePriceRenew", req, cb);
    }
    async StartCLSDelivery(req, cb) {
        return this.request("StartCLSDelivery", req, cb);
    }
    async ReloadBalanceProxyNode(req, cb) {
        return this.request("ReloadBalanceProxyNode", req, cb);
    }
    async DescribeBackupDownloadRestriction(req, cb) {
        return this.request("DescribeBackupDownloadRestriction", req, cb);
    }
    async OfflineCluster(req, cb) {
        return this.request("OfflineCluster", req, cb);
    }
    async DescribeClusterDatabaseTables(req, cb) {
        return this.request("DescribeClusterDatabaseTables", req, cb);
    }
    async StopCLSDelivery(req, cb) {
        return this.request("StopCLSDelivery", req, cb);
    }
    async DescribeBackupList(req, cb) {
        return this.request("DescribeBackupList", req, cb);
    }
    async ModifyAccountDescription(req, cb) {
        return this.request("ModifyAccountDescription", req, cb);
    }
    async ModifyServerlessStrategy(req, cb) {
        return this.request("ModifyServerlessStrategy", req, cb);
    }
    async CreateClusterDatabase(req, cb) {
        return this.request("CreateClusterDatabase", req, cb);
    }
    async OpenAuditService(req, cb) {
        return this.request("OpenAuditService", req, cb);
    }
    async ModifyClusterReadOnly(req, cb) {
        return this.request("ModifyClusterReadOnly", req, cb);
    }
    async DeleteAuditLogFile(req, cb) {
        return this.request("DeleteAuditLogFile", req, cb);
    }
    async DescribeAuditRuleTemplates(req, cb) {
        return this.request("DescribeAuditRuleTemplates", req, cb);
    }
    async DescribeResourcePackageDetail(req, cb) {
        return this.request("DescribeResourcePackageDetail", req, cb);
    }
    async ModifyBackupConfig(req, cb) {
        return this.request("ModifyBackupConfig", req, cb);
    }
    async DescribeInstances(req, cb) {
        return this.request("DescribeInstances", req, cb);
    }
    async DescribeBinlogConfig(req, cb) {
        return this.request("DescribeBinlogConfig", req, cb);
    }
    async UpgradeClusterVersion(req, cb) {
        return this.request("UpgradeClusterVersion", req, cb);
    }
    async OpenSSL(req, cb) {
        return this.request("OpenSSL", req, cb);
    }
    async DescribeResourcePackageList(req, cb) {
        return this.request("DescribeResourcePackageList", req, cb);
    }
    async DescribeClusterParams(req, cb) {
        return this.request("DescribeClusterParams", req, cb);
    }
    async RefundResourcePackage(req, cb) {
        return this.request("RefundResourcePackage", req, cb);
    }
    async ModifyInstanceName(req, cb) {
        return this.request("ModifyInstanceName", req, cb);
    }
    async DescribeMaintainPeriod(req, cb) {
        return this.request("DescribeMaintainPeriod", req, cb);
    }
    async ExportResourcePackageDeductDetails(req, cb) {
        return this.request("ExportResourcePackageDeductDetails", req, cb);
    }
    async DescribeClusterDetailDatabases(req, cb) {
        return this.request("DescribeClusterDetailDatabases", req, cb);
    }
    async ModifyAuditService(req, cb) {
        return this.request("ModifyAuditService", req, cb);
    }
    async DescribeZones(req, cb) {
        return this.request("DescribeZones", req, cb);
    }
    async DescribeBackupDownloadUrl(req, cb) {
        return this.request("DescribeBackupDownloadUrl", req, cb);
    }
    async SwitchClusterVpc(req, cb) {
        return this.request("SwitchClusterVpc", req, cb);
    }
    async CreateProxy(req, cb) {
        return this.request("CreateProxy", req, cb);
    }
    async ModifyBackupDownloadRestriction(req, cb) {
        return this.request("ModifyBackupDownloadRestriction", req, cb);
    }
    async CreateCLSDelivery(req, cb) {
        return this.request("CreateCLSDelivery", req, cb);
    }
    async ModifyBinlogSaveDays(req, cb) {
        return this.request("ModifyBinlogSaveDays", req, cb);
    }
    async DescribeAccountAllGrantPrivileges(req, cb) {
        return this.request("DescribeAccountAllGrantPrivileges", req, cb);
    }
    async OpenReadOnlyInstanceExclusiveAccess(req, cb) {
        return this.request("OpenReadOnlyInstanceExclusiveAccess", req, cb);
    }
    async DescribeClusterPasswordComplexity(req, cb) {
        return this.request("DescribeClusterPasswordComplexity", req, cb);
    }
    async DeleteParamTemplate(req, cb) {
        return this.request("DeleteParamTemplate", req, cb);
    }
    async UpgradeInstance(req, cb) {
        return this.request("UpgradeInstance", req, cb);
    }
    async DescribeBinlogs(req, cb) {
        return this.request("DescribeBinlogs", req, cb);
    }
    async DescribeClusterInstanceGroups(req, cb) {
        return this.request("DescribeClusterInstanceGroups", req, cb);
    }
    async DescribeDBSecurityGroups(req, cb) {
        return this.request("DescribeDBSecurityGroups", req, cb);
    }
    async OpenClusterPasswordComplexity(req, cb) {
        return this.request("OpenClusterPasswordComplexity", req, cb);
    }
    async ModifyResourcePackageName(req, cb) {
        return this.request("ModifyResourcePackageName", req, cb);
    }
    async RemoveClusterSlaveZone(req, cb) {
        return this.request("RemoveClusterSlaveZone", req, cb);
    }
    async CloseSSL(req, cb) {
        return this.request("CloseSSL", req, cb);
    }
    async DescribeParamTemplateDetail(req, cb) {
        return this.request("DescribeParamTemplateDetail", req, cb);
    }
    async IsolateInstance(req, cb) {
        return this.request("IsolateInstance", req, cb);
    }
}
