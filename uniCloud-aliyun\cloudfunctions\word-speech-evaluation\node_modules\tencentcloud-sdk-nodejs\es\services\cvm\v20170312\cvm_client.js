import { AbstractClient } from "../../../common/abstract_client";
export class Client extends AbstractClient {
    constructor(clientConfig) {
        super("cvm.tencentcloudapi.com", "2017-03-12", clientConfig);
    }
    async InquiryPriceRunInstances(req, cb) {
        return this.request("InquiryPriceRunInstances", req, cb);
    }
    async ModifyInstancesAttribute(req, cb) {
        return this.request("ModifyInstancesAttribute", req, cb);
    }
    async InquiryPriceResetInstancesInternetMaxBandwidth(req, cb) {
        return this.request("InquiryPriceResetInstancesInternetMaxBandwidth", req, cb);
    }
    async DeleteKeyPairs(req, cb) {
        return this.request("DeleteKeyPairs", req, cb);
    }
    async ImportKeyPair(req, cb) {
        return this.request("ImportKeyPair", req, cb);
    }
    async RunInstances(req, cb) {
        return this.request("RunInstances", req, cb);
    }
    async ModifyLaunchTemplateDefaultVersion(req, cb) {
        return this.request("ModifyLaunchTemplateDefaultVersion", req, cb);
    }
    async ModifyInstancesVpcAttribute(req, cb) {
        return this.request("ModifyInstancesVpcAttribute", req, cb);
    }
    async DescribeDisasterRecoverGroupQuota(req, cb) {
        return this.request("DescribeDisasterRecoverGroupQuota", req, cb);
    }
    async DescribeTaskInfo(req, cb) {
        return this.request("DescribeTaskInfo", req, cb);
    }
    async CreateImage(req, cb) {
        return this.request("CreateImage", req, cb);
    }
    async ConfigureChcAssistVpc(req, cb) {
        return this.request("ConfigureChcAssistVpc", req, cb);
    }
    async ResetInstancesType(req, cb) {
        return this.request("ResetInstancesType", req, cb);
    }
    async DescribeReservedInstancesConfigInfos(req, cb) {
        return this.request("DescribeReservedInstancesConfigInfos", req, cb);
    }
    async DescribeInstanceFamilyConfigs(req, cb) {
        return this.request("DescribeInstanceFamilyConfigs", req, cb);
    }
    async InquirePricePurchaseReservedInstancesOffering(req, cb) {
        return this.request("InquirePricePurchaseReservedInstancesOffering", req, cb);
    }
    async SyncImages(req, cb) {
        return this.request("SyncImages", req, cb);
    }
    async ImportImage(req, cb) {
        return this.request("ImportImage", req, cb);
    }
    async DescribeZoneInstanceConfigInfos(req, cb) {
        return this.request("DescribeZoneInstanceConfigInfos", req, cb);
    }
    async AllocateHosts(req, cb) {
        return this.request("AllocateHosts", req, cb);
    }
    async ModifyChcAttribute(req, cb) {
        return this.request("ModifyChcAttribute", req, cb);
    }
    async CreateHpcCluster(req, cb) {
        return this.request("CreateHpcCluster", req, cb);
    }
    async DisassociateSecurityGroups(req, cb) {
        return this.request("DisassociateSecurityGroups", req, cb);
    }
    async RemoveChcDeployVpc(req, cb) {
        return this.request("RemoveChcDeployVpc", req, cb);
    }
    async DescribeImageSharePermission(req, cb) {
        return this.request("DescribeImageSharePermission", req, cb);
    }
    async DescribeInstancesAttributes(req, cb) {
        return this.request("DescribeInstancesAttributes", req, cb);
    }
    async DescribeInstancesModification(req, cb) {
        return this.request("DescribeInstancesModification", req, cb);
    }
    async ExitRescueMode(req, cb) {
        return this.request("ExitRescueMode", req, cb);
    }
    async AssociateInstancesKeyPairs(req, cb) {
        return this.request("AssociateInstancesKeyPairs", req, cb);
    }
    async DisassociateInstancesKeyPairs(req, cb) {
        return this.request("DisassociateInstancesKeyPairs", req, cb);
    }
    async DeleteInstancesActionTimer(req, cb) {
        return this.request("DeleteInstancesActionTimer", req, cb);
    }
    async CreateLaunchTemplate(req, cb) {
        return this.request("CreateLaunchTemplate", req, cb);
    }
    async ModifyDisasterRecoverGroupAttribute(req, cb) {
        return this.request("ModifyDisasterRecoverGroupAttribute", req, cb);
    }
    async DescribeHpcClusters(req, cb) {
        return this.request("DescribeHpcClusters", req, cb);
    }
    async DescribeInstancesOperationLimit(req, cb) {
        return this.request("DescribeInstancesOperationLimit", req, cb);
    }
    async ModifyHostsAttribute(req, cb) {
        return this.request("ModifyHostsAttribute", req, cb);
    }
    async DescribeRegions(req, cb) {
        return this.request("DescribeRegions", req, cb);
    }
    async DescribeImportImageOs(req, cb) {
        return this.request("DescribeImportImageOs", req, cb);
    }
    async DeleteHpcClusters(req, cb) {
        return this.request("DeleteHpcClusters", req, cb);
    }
    async InquiryPriceRenewInstances(req, cb) {
        return this.request("InquiryPriceRenewInstances", req, cb);
    }
    async ModifyInstancesChargeType(req, cb) {
        return this.request("ModifyInstancesChargeType", req, cb);
    }
    async RenewHosts(req, cb) {
        return this.request("RenewHosts", req, cb);
    }
    async RepairTaskControl(req, cb) {
        return this.request("RepairTaskControl", req, cb);
    }
    async DescribeHosts(req, cb) {
        return this.request("DescribeHosts", req, cb);
    }
    async EnterRescueMode(req, cb) {
        return this.request("EnterRescueMode", req, cb);
    }
    async DescribeInstancesStatus(req, cb) {
        return this.request("DescribeInstancesStatus", req, cb);
    }
    async ModifyImageSharePermission(req, cb) {
        return this.request("ModifyImageSharePermission", req, cb);
    }
    async ImportInstancesActionTimer(req, cb) {
        return this.request("ImportInstancesActionTimer", req, cb);
    }
    async DeleteLaunchTemplate(req, cb) {
        return this.request("DeleteLaunchTemplate", req, cb);
    }
    async ModifyInstancesDisasterRecoverGroup(req, cb) {
        return this.request("ModifyInstancesDisasterRecoverGroup", req, cb);
    }
    async CreateKeyPair(req, cb) {
        return this.request("CreateKeyPair", req, cb);
    }
    async DescribeInstancesActionTimer(req, cb) {
        return this.request("DescribeInstancesActionTimer", req, cb);
    }
    async InquiryPriceResizeInstanceDisks(req, cb) {
        return this.request("InquiryPriceResizeInstanceDisks", req, cb);
    }
    async InquiryPriceResetInstance(req, cb) {
        return this.request("InquiryPriceResetInstance", req, cb);
    }
    async PurchaseReservedInstancesOffering(req, cb) {
        return this.request("PurchaseReservedInstancesOffering", req, cb);
    }
    async ModifyInstancesRenewFlag(req, cb) {
        return this.request("ModifyInstancesRenewFlag", req, cb);
    }
    async ResetInstance(req, cb) {
        return this.request("ResetInstance", req, cb);
    }
    async StartInstances(req, cb) {
        return this.request("StartInstances", req, cb);
    }
    async DescribeLaunchTemplates(req, cb) {
        return this.request("DescribeLaunchTemplates", req, cb);
    }
    async AssociateSecurityGroups(req, cb) {
        return this.request("AssociateSecurityGroups", req, cb);
    }
    async ModifyImageAttribute(req, cb) {
        return this.request("ModifyImageAttribute", req, cb);
    }
    async ConvertOperatingSystems(req, cb) {
        return this.request("ConvertOperatingSystems", req, cb);
    }
    async DeleteDisasterRecoverGroups(req, cb) {
        return this.request("DeleteDisasterRecoverGroups", req, cb);
    }
    async TerminateInstances(req, cb) {
        return this.request("TerminateInstances", req, cb);
    }
    async RemoveChcAssistVpc(req, cb) {
        return this.request("RemoveChcAssistVpc", req, cb);
    }
    async DescribeInstanceVncUrl(req, cb) {
        return this.request("DescribeInstanceVncUrl", req, cb);
    }
    async ModifyInstancesProject(req, cb) {
        return this.request("ModifyInstancesProject", req, cb);
    }
    async DescribeKeyPairs(req, cb) {
        return this.request("DescribeKeyPairs", req, cb);
    }
    async DescribeReservedInstancesOfferings(req, cb) {
        return this.request("DescribeReservedInstancesOfferings", req, cb);
    }
    async InquiryPriceModifyInstancesChargeType(req, cb) {
        return this.request("InquiryPriceModifyInstancesChargeType", req, cb);
    }
    async DescribeInternetChargeTypeConfigs(req, cb) {
        return this.request("DescribeInternetChargeTypeConfigs", req, cb);
    }
    async RebootInstances(req, cb) {
        return this.request("RebootInstances", req, cb);
    }
    async InquiryPriceRenewHosts(req, cb) {
        return this.request("InquiryPriceRenewHosts", req, cb);
    }
    async DescribeImageFromFamily(req, cb) {
        return this.request("DescribeImageFromFamily", req, cb);
    }
    async StopInstances(req, cb) {
        return this.request("StopInstances", req, cb);
    }
    async ModifyHpcClusterAttribute(req, cb) {
        return this.request("ModifyHpcClusterAttribute", req, cb);
    }
    async ModifyKeyPairAttribute(req, cb) {
        return this.request("ModifyKeyPairAttribute", req, cb);
    }
    async DescribeImages(req, cb) {
        return this.request("DescribeImages", req, cb);
    }
    async CreateDisasterRecoverGroup(req, cb) {
        return this.request("CreateDisasterRecoverGroup", req, cb);
    }
    async DescribeInstances(req, cb) {
        return this.request("DescribeInstances", req, cb);
    }
    async DescribeLaunchTemplateVersions(req, cb) {
        return this.request("DescribeLaunchTemplateVersions", req, cb);
    }
    async DescribeInstanceInternetBandwidthConfigs(req, cb) {
        return this.request("DescribeInstanceInternetBandwidthConfigs", req, cb);
    }
    async InquiryPriceTerminateInstances(req, cb) {
        return this.request("InquiryPriceTerminateInstances", req, cb);
    }
    async DeleteImages(req, cb) {
        return this.request("DeleteImages", req, cb);
    }
    async DeleteLaunchTemplateVersions(req, cb) {
        return this.request("DeleteLaunchTemplateVersions", req, cb);
    }
    async ResetInstancesPassword(req, cb) {
        return this.request("ResetInstancesPassword", req, cb);
    }
    async ResizeInstanceDisks(req, cb) {
        return this.request("ResizeInstanceDisks", req, cb);
    }
    async DescribeZones(req, cb) {
        return this.request("DescribeZones", req, cb);
    }
    async DescribeImageQuota(req, cb) {
        return this.request("DescribeImageQuota", req, cb);
    }
    async DescribeChcDeniedActions(req, cb) {
        return this.request("DescribeChcDeniedActions", req, cb);
    }
    async DescribeAccountQuota(req, cb) {
        return this.request("DescribeAccountQuota", req, cb);
    }
    async ConfigureChcDeployVpc(req, cb) {
        return this.request("ConfigureChcDeployVpc", req, cb);
    }
    async InquiryPriceResetInstancesType(req, cb) {
        return this.request("InquiryPriceResetInstancesType", req, cb);
    }
    async ProgramFpgaImage(req, cb) {
        return this.request("ProgramFpgaImage", req, cb);
    }
    async CreateLaunchTemplateVersion(req, cb) {
        return this.request("CreateLaunchTemplateVersion", req, cb);
    }
    async ExportImages(req, cb) {
        return this.request("ExportImages", req, cb);
    }
    async RenewInstances(req, cb) {
        return this.request("RenewInstances", req, cb);
    }
    async ResetInstancesInternetMaxBandwidth(req, cb) {
        return this.request("ResetInstancesInternetMaxBandwidth", req, cb);
    }
    async DescribeDisasterRecoverGroups(req, cb) {
        return this.request("DescribeDisasterRecoverGroups", req, cb);
    }
    async DescribeChcHosts(req, cb) {
        return this.request("DescribeChcHosts", req, cb);
    }
    async DescribeInstanceTypeConfigs(req, cb) {
        return this.request("DescribeInstanceTypeConfigs", req, cb);
    }
    async ModifyInstanceDiskType(req, cb) {
        return this.request("ModifyInstanceDiskType", req, cb);
    }
}
