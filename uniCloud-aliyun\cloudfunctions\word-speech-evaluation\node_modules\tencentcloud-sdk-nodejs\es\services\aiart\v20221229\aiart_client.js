import { AbstractClient } from "../../../common/abstract_client";
export class Client extends AbstractClient {
    constructor(clientConfig) {
        super("aiart.tencentcloudapi.com", "2022-12-29", clientConfig);
    }
    async SubmitMemeJob(req, cb) {
        return this.request("SubmitMemeJob", req, cb);
    }
    async RefineImage(req, cb) {
        return this.request("RefineImage", req, cb);
    }
    async SketchToImage(req, cb) {
        return this.request("SketchToImage", req, cb);
    }
    async SubmitTextToImageProJob(req, cb) {
        return this.request("SubmitTextToImageProJob", req, cb);
    }
    async ReplaceBackground(req, cb) {
        return this.request("ReplaceBackground", req, cb);
    }
    async TextToImageRapid(req, cb) {
        return this.request("TextToImageRapid", req, cb);
    }
    async SubmitTrainPortraitModelJob(req, cb) {
        return this.request("SubmitTrainPortraitModelJob", req, cb);
    }
    async ImageOutpainting(req, cb) {
        return this.request("ImageOutpainting", req, cb);
    }
    async GenerateAvatar(req, cb) {
        return this.request("GenerateAvatar", req, cb);
    }
    async QueryMemeJob(req, cb) {
        return this.request("QueryMemeJob", req, cb);
    }
    async QueryTrainPortraitModelJob(req, cb) {
        return this.request("QueryTrainPortraitModelJob", req, cb);
    }
    async QueryDrawPortraitJob(req, cb) {
        return this.request("QueryDrawPortraitJob", req, cb);
    }
    async QueryGlamPicJob(req, cb) {
        return this.request("QueryGlamPicJob", req, cb);
    }
    async UploadTrainPortraitImages(req, cb) {
        return this.request("UploadTrainPortraitImages", req, cb);
    }
    async QueryTextToImageProJob(req, cb) {
        return this.request("QueryTextToImageProJob", req, cb);
    }
    async SubmitGlamPicJob(req, cb) {
        return this.request("SubmitGlamPicJob", req, cb);
    }
    async SubmitDrawPortraitJob(req, cb) {
        return this.request("SubmitDrawPortraitJob", req, cb);
    }
    async ImageInpaintingRemoval(req, cb) {
        return this.request("ImageInpaintingRemoval", req, cb);
    }
    async ChangeClothes(req, cb) {
        return this.request("ChangeClothes", req, cb);
    }
    async TextToImageLite(req, cb) {
        return this.request("TextToImageLite", req, cb);
    }
    async ImageToImage(req, cb) {
        return this.request("ImageToImage", req, cb);
    }
}
