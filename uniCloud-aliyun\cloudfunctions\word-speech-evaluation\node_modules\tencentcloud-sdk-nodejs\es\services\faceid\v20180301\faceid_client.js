import { AbstractClient } from "../../../common/abstract_client";
export class Client extends AbstractClient {
    constructor(clientConfig) {
        super("faceid.tencentcloudapi.com", "2018-03-01", clientConfig);
    }
    async GetLiveCode(req, cb) {
        return this.request("GetLiveCode", req, cb);
    }
    async GetFaceIdRiskInfo(req, cb) {
        return this.request("GetFaceIdRiskInfo", req, cb);
    }
    async GetWeChatBillDetails(req, cb) {
        return this.request("GetWeChatBillDetails", req, cb);
    }
    async CheckIdCardInformation(req, cb) {
        return this.request("CheckIdCardInformation", req, cb);
    }
    async CheckEidTokenStatus(req, cb) {
        return this.request("CheckEidTokenStatus", req, cb);
    }
    async BankCard4EVerification(req, cb) {
        return this.request("BankCard4EVerification", req, cb);
    }
    async EncryptedPhoneVerification(req, cb) {
        return this.request("EncryptedPhoneVerification", req, cb);
    }
    async GetActionSequence(req, cb) {
        return this.request("GetActionSequence", req, cb);
    }
    async GetDetectInfoEnhanced(req, cb) {
        return this.request("GetDetectInfoEnhanced", req, cb);
    }
    async GetFaceIdToken(req, cb) {
        return this.request("GetFaceIdToken", req, cb);
    }
    async CheckPhoneAndName(req, cb) {
        return this.request("CheckPhoneAndName", req, cb);
    }
    async BankCardVerification(req, cb) {
        return this.request("BankCardVerification", req, cb);
    }
    async ImageRecognitionV2(req, cb) {
        return this.request("ImageRecognitionV2", req, cb);
    }
    async LivenessCompare(req, cb) {
        return this.request("LivenessCompare", req, cb);
    }
    async IdCardOCRVerification(req, cb) {
        return this.request("IdCardOCRVerification", req, cb);
    }
    async GetFaceIdResult(req, cb) {
        return this.request("GetFaceIdResult", req, cb);
    }
    async ImageRecognition(req, cb) {
        return this.request("ImageRecognition", req, cb);
    }
    async GetEidToken(req, cb) {
        return this.request("GetEidToken", req, cb);
    }
    async DetectAIFakeFaces(req, cb) {
        return this.request("DetectAIFakeFaces", req, cb);
    }
    async PhoneVerification(req, cb) {
        return this.request("PhoneVerification", req, cb);
    }
    async MobileStatus(req, cb) {
        return this.request("MobileStatus", req, cb);
    }
    async CheckIdNameDate(req, cb) {
        return this.request("CheckIdNameDate", req, cb);
    }
    async PhoneVerificationCMCC(req, cb) {
        return this.request("PhoneVerificationCMCC", req, cb);
    }
    async MinorsVerification(req, cb) {
        return this.request("MinorsVerification", req, cb);
    }
    async GetFaceidRiskInfoToken(req, cb) {
        return this.request("GetFaceidRiskInfoToken", req, cb);
    }
    async Liveness(req, cb) {
        return this.request("Liveness", req, cb);
    }
    async PhoneVerificationCUCC(req, cb) {
        return this.request("PhoneVerificationCUCC", req, cb);
    }
    async LivenessRecognition(req, cb) {
        return this.request("LivenessRecognition", req, cb);
    }
    async ParseNfcData(req, cb) {
        return this.request("ParseNfcData", req, cb);
    }
    async IdCardVerification(req, cb) {
        return this.request("IdCardVerification", req, cb);
    }
    async MobileNetworkTimeVerification(req, cb) {
        return this.request("MobileNetworkTimeVerification", req, cb);
    }
    async GetEidResult(req, cb) {
        return this.request("GetEidResult", req, cb);
    }
    async DetectAuth(req, cb) {
        return this.request("DetectAuth", req, cb);
    }
    async CheckBankCardInformation(req, cb) {
        return this.request("CheckBankCardInformation", req, cb);
    }
    async GetDetectInfo(req, cb) {
        return this.request("GetDetectInfo", req, cb);
    }
    async BankCard2EVerification(req, cb) {
        return this.request("BankCard2EVerification", req, cb);
    }
    async PhoneVerificationCTCC(req, cb) {
        return this.request("PhoneVerificationCTCC", req, cb);
    }
}
