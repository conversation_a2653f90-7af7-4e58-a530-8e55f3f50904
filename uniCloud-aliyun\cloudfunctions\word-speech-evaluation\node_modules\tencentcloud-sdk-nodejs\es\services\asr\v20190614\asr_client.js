import { AbstractClient } from "../../../common/abstract_client";
export class Client extends AbstractClient {
    constructor(clientConfig) {
        super("asr.tencentcloudapi.com", "2019-06-14", clientConfig);
    }
    async GetUsageByDate(req, cb) {
        return this.request("GetUsageByDate", req, cb);
    }
    async VoicePrintCompare(req, cb) {
        return this.request("VoicePrintCompare", req, cb);
    }
    async UpdateAsrVocab(req, cb) {
        return this.request("UpdateAsrVocab", req, cb);
    }
    async CreateAsyncRecognitionTask(req, cb) {
        return this.request("CreateAsyncRecognitionTask", req, cb);
    }
    async GetCustomizationList(req, cb) {
        return this.request("GetCustomizationList", req, cb);
    }
    async VoicePrintCount(req, cb) {
        return this.request("VoicePrintCount", req, cb);
    }
    async GetAsrVocabList(req, cb) {
        return this.request("GetAsrVocabList", req, cb);
    }
    async GetAsrVocab(req, cb) {
        return this.request("GetAsrVocab", req, cb);
    }
    async SetVocabState(req, cb) {
        return this.request("SetVocabState", req, cb);
    }
    async DownloadAsrVocab(req, cb) {
        return this.request("DownloadAsrVocab", req, cb);
    }
    async CreateCustomization(req, cb) {
        return this.request("CreateCustomization", req, cb);
    }
    async VoicePrintGroupVerify(req, cb) {
        return this.request("VoicePrintGroupVerify", req, cb);
    }
    async DeleteAsrVocab(req, cb) {
        return this.request("DeleteAsrVocab", req, cb);
    }
    async GetAsrKeyWordLibList(req, cb) {
        return this.request("GetAsrKeyWordLibList", req, cb);
    }
    async SentenceRecognition(req, cb) {
        return this.request("SentenceRecognition", req, cb);
    }
    async UpdateAsrKeyWordLib(req, cb) {
        return this.request("UpdateAsrKeyWordLib", req, cb);
    }
    async VoicePrintUpdate(req, cb) {
        return this.request("VoicePrintUpdate", req, cb);
    }
    async CreateRecTask(req, cb) {
        return this.request("CreateRecTask", req, cb);
    }
    async DescribeTaskStatus(req, cb) {
        return this.request("DescribeTaskStatus", req, cb);
    }
    async VoicePrintEnroll(req, cb) {
        return this.request("VoicePrintEnroll", req, cb);
    }
    async VoicePrintVerify(req, cb) {
        return this.request("VoicePrintVerify", req, cb);
    }
    async DescribeAsyncRecognitionTasks(req, cb) {
        return this.request("DescribeAsyncRecognitionTasks", req, cb);
    }
    async VoicePrintDelete(req, cb) {
        return this.request("VoicePrintDelete", req, cb);
    }
    async CreateAsrKeyWordLib(req, cb) {
        return this.request("CreateAsrKeyWordLib", req, cb);
    }
    async ModifyCustomization(req, cb) {
        return this.request("ModifyCustomization", req, cb);
    }
    async CloseAsyncRecognitionTask(req, cb) {
        return this.request("CloseAsyncRecognitionTask", req, cb);
    }
    async GetModelInfo(req, cb) {
        return this.request("GetModelInfo", req, cb);
    }
    async DeleteAsrKeyWordLib(req, cb) {
        return this.request("DeleteAsrKeyWordLib", req, cb);
    }
    async ModifyCustomizationState(req, cb) {
        return this.request("ModifyCustomizationState", req, cb);
    }
    async CreateAsrVocab(req, cb) {
        return this.request("CreateAsrVocab", req, cb);
    }
    async DeleteCustomization(req, cb) {
        return this.request("DeleteCustomization", req, cb);
    }
    async DownloadCustomization(req, cb) {
        return this.request("DownloadCustomization", req, cb);
    }
}
